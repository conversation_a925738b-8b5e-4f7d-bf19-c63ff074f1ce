package hero.connect.oauth

import dev.forkhandles.result4k.Failure
import dev.forkhandles.result4k.Result
import dev.forkhandles.result4k.Success
import hero.baseutils.plusMinutes
import hero.http4k.auth.getJwtUser
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.OauthAuthorizationCodeRecord
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Uri
import org.http4k.security.State
import org.http4k.security.oauth.server.AuthRequest
import org.http4k.security.oauth.server.AuthorizationCode
import org.http4k.security.oauth.server.AuthorizationCodeDetails
import org.http4k.security.oauth.server.AuthorizationCodes
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.UserRejectedRequest
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.Random
import java.util.UUID

class OAuthAuthorizationCodeService(
    lazyContext: Lazy<DSLContext>,
    private val random: Random,
    private val clock: Clock = Clock.systemUTC(),
) : AuthorizationCodes {
    private val context by lazyContext

    override fun create(
        request: Request,
        authRequest: AuthRequest,
        response: Response,
    ): Result<AuthorizationCode, UserRejectedRequest> {
        if (userRejectedFormField(authorizeFormBody[request])) {
            return Failure(UserRejectedRequest)
        }
        val now = Instant.now(clock)
        val user = request.getJwtUser(allowImpersonation = false)

        val record = OauthAuthorizationCodeRecord().apply {
            this.id = generateRandomId(random)
            this.clientId = UUID.fromString(authRequest.client.value)
            this.userId = user.id
            this.redirectUri = authRequest.redirectUri.toString()
            this.state = authRequest.state?.value
            this.scopes = authRequest.scopes.toTypedArray()
            this.responseType = authRequest.responseType.name
            this.responseMode = authRequest.responseMode?.name
            // will be always null since we don't support PKCE atm
            this.codeChallenge = null
            this.createdAt = now
            this.updatedAt = now
            this.usedAt = null
        }

        context
            .insertInto(Tables.OAUTH_AUTHORIZATION_CODE)
            .set(record)
            .execute()

        return Success(AuthorizationCode(record.id))
    }

    override fun detailsFor(code: AuthorizationCode): AuthorizationCodeDetails {
        val fetchedCode = context
            .selectFrom(Tables.OAUTH_AUTHORIZATION_CODE)
            .where(Tables.OAUTH_AUTHORIZATION_CODE.ID.eq(code.value))
            .fetchOne()
            ?: error("Missing code ${code.value}")

        return AuthorizationCodeDetails(
            clientId = ClientId(fetchedCode.clientId.toString()),
            redirectUri = Uri.of(fetchedCode.redirectUri),
            expiresAt = fetchedCode.createdAt.plusMinutes(10),
            state = fetchedCode.state?.let { State(it) },
            isOIDC = false,
        )
    }
}
