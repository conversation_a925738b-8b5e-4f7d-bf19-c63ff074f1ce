package hero.connect.oauth

import dev.forkhandles.result4k.Failure
import dev.forkhandles.result4k.Result
import dev.forkhandles.result4k.Success
import hero.baseutils.plusDays
import hero.jwt.TokenType
import hero.jwt.generateJwt
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.OAUTH_AUTHORIZATION_CODE
import hero.sql.jooq.Tables.OAUTH_CLIENT
import hero.sql.jooq.tables.records.OauthRefreshTokenRecord
import org.http4k.security.AccessToken
import org.http4k.security.oauth.core.RefreshToken
import org.http4k.security.oauth.server.AccessTokenError
import org.http4k.security.oauth.server.AccessTokens
import org.http4k.security.oauth.server.AuthorizationCodeAlreadyUsed
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.ClientIdMismatch
import org.http4k.security.oauth.server.InvalidRequest
import org.http4k.security.oauth.server.RedirectUriMismatch
import org.http4k.security.oauth.server.TokenRequest
import org.http4k.security.oauth.server.UnsupportedGrantType
import org.http4k.security.oauth.server.accesstoken.AuthorizationCodeAccessTokenRequest
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.Random
import javax.crypto.SecretKey

class OAuthAccessTokenGenerator(
    lazyContext: Lazy<DSLContext>,
    private val random: Random,
    private val secretKey: SecretKey,
    private val clock: Clock = Clock.systemUTC(),
) : AccessTokens {
    private val context by lazyContext

    override fun create(
        clientId: ClientId,
        tokenRequest: AuthorizationCodeAccessTokenRequest,
    ): Result<AccessToken, AccessTokenError> {
        val authorizationCode = context
            .select(
                OAUTH_AUTHORIZATION_CODE.ID,
                OAUTH_AUTHORIZATION_CODE.USER_ID,
                OAUTH_AUTHORIZATION_CODE.USED_AT,
                OAUTH_AUTHORIZATION_CODE.CLIENT_ID,
                OAUTH_AUTHORIZATION_CODE.REDIRECT_URI,
                OAUTH_AUTHORIZATION_CODE.SCOPES,
                OAUTH_CLIENT.USER_ID,
            )
            .from(OAUTH_AUTHORIZATION_CODE)
            .join(OAUTH_CLIENT)
            .on(OAUTH_AUTHORIZATION_CODE.CLIENT_ID.eq(OAUTH_CLIENT.ID))
            .where(OAUTH_AUTHORIZATION_CODE.ID.eq(tokenRequest.authorizationCode.value))
            .fetchOne()
            ?: return Failure(InvalidRequest("Authorization code ${tokenRequest.authorizationCode} is not valid"))

        if (authorizationCode[OAUTH_AUTHORIZATION_CODE.USED_AT] != null) {
            // we should invalidate all refresh tokens here, we should assume that authorization code was stolen
            return Failure(AuthorizationCodeAlreadyUsed)
        }

        if (authorizationCode[OAUTH_AUTHORIZATION_CODE.CLIENT_ID].toString() != clientId.value) {
            return Failure(ClientIdMismatch)
        }

        if (authorizationCode[OAUTH_AUTHORIZATION_CODE.REDIRECT_URI] != tokenRequest.redirectUri.toString()) {
            return Failure(RedirectUriMismatch)
        }

        val now = Instant.now(clock)
        val refreshTokenRecord = OauthRefreshTokenRecord().apply {
            this.id = generateRandomId(random)
            this.userId = authorizationCode[OAUTH_AUTHORIZATION_CODE.USER_ID]
            this.authorizationCodeId = authorizationCode[OAUTH_AUTHORIZATION_CODE.ID]
            this.createdAt = now
            this.refreshedAt = now
        }

        context.transaction { config ->
            config
                .dsl()
                .update(OAUTH_AUTHORIZATION_CODE)
                .set(OAUTH_AUTHORIZATION_CODE.USED_AT, now)
                .set(OAUTH_AUTHORIZATION_CODE.UPDATED_AT, now)
                .where(OAUTH_AUTHORIZATION_CODE.ID.eq(authorizationCode[OAUTH_AUTHORIZATION_CODE.ID]))
                .execute()

            config
                .dsl()
                .insertInto(Tables.OAUTH_REFRESH_TOKEN)
                .set(refreshTokenRecord)
                .execute()
        }

        val accessToken = generateJwt(
            authorizationCode[OAUTH_AUTHORIZATION_CODE.USER_ID],
            now.plusDays(30).epochSecond,
            TokenType.ACCESS,
            additionalClaims = mapOf(
                "scopes" to authorizationCode[OAUTH_AUTHORIZATION_CODE.SCOPES],
                "creatorId" to authorizationCode[OAUTH_CLIENT.USER_ID],
                "clientId" to authorizationCode[OAUTH_AUTHORIZATION_CODE.CLIENT_ID].toString(),
            ),
            key = secretKey,
            issuedAt = now,
        )

        return Success(
            AccessToken(
                value = accessToken,
                expiresIn = 60 * 60 * 24 * 30,
                refreshToken = RefreshToken(refreshTokenRecord.id),
            ),
        )
    }

    override fun create(
        clientId: ClientId,
        tokenRequest: TokenRequest,
    ): Result<AccessToken, AccessTokenError> = Failure(UnsupportedGrantType(tokenRequest.grantType.name))
}
