package hero.connect.oauth

import dev.forkhandles.result4k.Failure
import dev.forkhandles.result4k.Result
import dev.forkhandles.result4k.Success
import hero.model.OAuthScopes
import org.http4k.core.Request
import org.http4k.core.Uri
import org.http4k.security.oauth.server.AuthRequest
import org.http4k.security.oauth.server.AuthoriseRequestValidator
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.InvalidClientId
import org.http4k.security.oauth.server.InvalidRedirectUri
import org.http4k.security.oauth.server.InvalidScopes
import org.http4k.security.oauth.server.OAuthError
import org.jooq.DSLContext

class OAuthClientValidator(lazyContext: Lazy<DSLContext>) : AuthoriseRequestValidator {
    private val context by lazyContext

    override fun isValidClientAndRedirectUriInCaseOfError(
        request: Request,
        clientId: ClientId,
        redirectUri: Uri,
    ): Boolean {
        val client = context.getClient(clientId.value) ?: return false

        return redirectUri.toString() in client.redirectUris
    }

    override fun validate(
        request: Request,
        authorizationRequest: AuthRequest,
    ): Result<Request, OAuthError> {
        val client = context.getClient(authorizationRequest.client.value) ?: return Failure(InvalidClientId)
        val redirectUri = authorizationRequest.redirectUri
        return if (redirectUri != null && redirectUri.toString() !in client.redirectUris) {
            Failure(InvalidRedirectUri)
        } else if (!ALLOWED_SCOPES.containsAll(authorizationRequest.scopes)) {
            Failure(InvalidScopes)
        } else {
            Success(request)
        }
    }
}

val ALLOWED_SCOPES = OAuthScopes.entries.map { it.value }
