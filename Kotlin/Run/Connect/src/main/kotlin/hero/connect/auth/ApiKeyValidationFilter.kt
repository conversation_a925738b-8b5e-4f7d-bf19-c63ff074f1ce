package hero.connect.auth

import hero.exceptions.http.UnauthorizedException
import hero.sql.jooq.Tables
import org.http4k.core.Filter
import org.jooq.DSLContext
import java.time.Instant

fun validateApiKey(lazyContext: Lazy<DSLContext>) =
    Filter { next ->
        {
            val authorizationHeader = it.header("Authorization") ?: throw UnauthorizedException()
            val bearerDelimiter = "Bearer "
            if (!authorizationHeader.startsWith(bearerDelimiter)) {
                throw UnauthorizedException()
            }
            val token = authorizationHeader.substringAfter(bearerDelimiter)

            val context = lazyContext.value
            val apiKey = context
                .selectFrom(Tables.API_KEY)
                .where(Tables.API_KEY.KEY.eq(token))
                .fetchOne()
                ?: throw UnauthorizedException()

            if (apiKey.revokedAt != null || apiKey.expiresAt?.isBefore(Instant.now()) == true) {
                throw UnauthorizedException()
            }

            val augmentedRequest = it.header("userId", apiKey.userId)
            val response = next(augmentedRequest)

            context
                .update(Tables.API_KEY)
                .set(Tables.API_KEY.LAST_USED_AT, Instant.now())
                .where(Tables.API_KEY.ID.eq(apiKey.id))
                .execute()

            response
        }
    }
