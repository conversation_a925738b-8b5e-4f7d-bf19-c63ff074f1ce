package hero.connect.oauth

import dev.forkhandles.result4k.failureOrNull
import dev.forkhandles.result4k.valueOrNull
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Uri
import org.http4k.security.oauth.server.AuthRequest
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.InvalidClientId
import org.http4k.security.oauth.server.InvalidRedirectUri
import org.http4k.security.oauth.server.InvalidScopes
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class OAuthClientValidatorIT : IntegrationTest() {
    private val request = Request(Method.GET, "")

    @Nested
    inner class IsValidClientAndRedirectUriInCaseOfError {
        @Test
        fun `should return false if invalid redirect url is passed`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                redirectUris = listOf("https://some-super-valid.url"),
            )

            val result = underTest.isValidClientAndRedirectUriInCaseOfError(
                request,
                ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                Uri.of("https://invalid.url"),
            )

            assertThat(result).isFalse()
        }

        @Test
        fun `should return true if valid redirect url is passed`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                redirectUris = listOf("https://some-super-valid.url"),
            )

            val result = underTest.isValidClientAndRedirectUriInCaseOfError(
                request,
                ClientId("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                Uri.of("https://some-super-valid.url"),
            )

            assertThat(result).isTrue()
        }
    }

    @Nested
    inner class Validate {
        @Test
        fun `should fail if client does not exist`() {
            val underTest = OAuthClientValidator(lazyTestContext)

            val result = underTest.validate(request, authRequest(clientId = "random-id"))

            assertThat(result.failureOrNull()).isEqualTo(InvalidClientId)
        }

        @Test
        fun `should fail if client is disabled`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                disabledAt = Instant.now(),
            )

            val result = underTest.validate(request, authRequest(clientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))

            assertThat(result.failureOrNull()).isEqualTo(InvalidClientId)
        }

        @Test
        fun `should fail if client is deleted`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                deletedAt = Instant.now(),
            )

            val result = underTest.validate(request, authRequest(clientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))

            assertThat(result.failureOrNull()).isEqualTo(InvalidClientId)
        }

        @Test
        fun `should fail if invalid redirect url is passed`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                redirectUris = listOf("https://some-super-valid.url"),
            )

            val authorizationRequest = authRequest(
                clientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
                redirectUri = "https://invalid.url",
            )
            val result = underTest.validate(request, authorizationRequest)

            assertThat(result.failureOrNull()).isEqualTo(InvalidRedirectUri)
        }

        @Test
        fun `should fail if invalid scope is passed`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"))

            val authorizationRequest = authRequest(
                clientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
                scopes = listOf("ab"),
            )
            val result = underTest.validate(request, authorizationRequest)

            assertThat(result.failureOrNull()).isEqualTo(InvalidScopes)
        }

        @Test
        fun `should succeed if all values are valid`() {
            val underTest = OAuthClientValidator(lazyTestContext)
            testHelper.createOAuthClient(
                id = UUID.fromString("042842bf-ecb8-44d3-ba61-c400c7d7e2fe"),
                redirectUris = listOf("https://some-super-valid.url"),
            )

            val authorizationRequest = authRequest(
                clientId = "042842bf-ecb8-44d3-ba61-c400c7d7e2fe",
                scopes = listOf("subscription.read"),
                redirectUri = "https://some-super-valid.url",
            )
            val result = underTest.validate(request, authorizationRequest)

            assertThat(result.valueOrNull()).isEqualTo(request)
        }
    }

    private fun authRequest(
        clientId: String = "client-id",
        redirectUri: String? = null,
        scopes: List<String> = ALLOWED_SCOPES,
    ) = AuthRequest(
        client = ClientId(clientId),
        scopes = scopes,
        redirectUri = redirectUri?.let { Uri.of(it) },
        state = null,
    )
}
