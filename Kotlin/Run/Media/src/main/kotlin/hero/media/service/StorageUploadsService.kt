package hero.media.service

import com.github.kittinunf.fuel.httpGet
import com.google.auth.oauth2.ServiceAccountCredentials
import com.google.cloud.storage.BlobId
import com.google.cloud.storage.BlobInfo
import com.google.cloud.storage.HttpMethod
import com.google.cloud.storage.Storage
import com.google.cloud.storage.StorageOptions
import hero.baseutils.envPrefix
import hero.baseutils.fuelUserAgent
import hero.exceptions.http.BadRequestException
import hero.media.service.StorageUploadsService.AssetType.DOCUMENT
import hero.media.service.StorageUploadsService.AssetType.IMAGE
import hero.model.DocumentType
import java.time.Instant
import java.util.concurrent.TimeUnit

class StorageUploadsService(
    projectId: String,
    private val production: Boolean,
) {
    internal val storage: Storage = StorageOptions
        .newBuilder()
        .setProjectId(projectId)
        .build()
        .service

    enum class AssetType {
        IMAGE,
        DOCUMENT,
    }

    private val storageUploadCredentials = ServiceAccountCredentials
        .fromStream(this.javaClass.classLoader.getResourceAsStream("storage-uploader-key.json"))

    fun objectName(
        entityName: String,
        userId: String,
        contentType: String,
        nonce: String,
    ): UploadInfo {
        val path = when (contentType.mediaType) {
            IMAGE -> "${production.envPrefix}/images/${entityName.lowercase()}/$userId/"
            DOCUMENT -> "${production.envPrefix}/documents/${entityName.lowercase()}/$userId/"
        }
        val suffix = when (contentType.mediaType) {
            IMAGE, DOCUMENT -> contentType.split("/").last()
        }

        val id = Instant.now().let { "${it.epochSecond}-${it.nano}-$nonce" }
        val objectName = "$path$id.$suffix"

        return UploadInfo(
            id = id,
            objectName = objectName,
            contentType = contentType,
        )
    }

    fun uploadUrl(objectName: String): String {
        val signedUrl = storage
            .signUrl(
                BlobInfo.newBuilder(BlobId.of(ASSETS_BUCKET, objectName)).build(),
                URL_EXPIRATION_IN_MINUTES,
                TimeUnit.MINUTES,
                Storage.SignUrlOption.httpMethod(HttpMethod.PUT),
                Storage.SignUrlOption.withExtHeaders(emptyMap()),
                Storage.SignUrlOption.signWith(storageUploadCredentials),
                Storage.SignUrlOption.withV4Signature(),
            )
            .toString()

        // images and others are directly uploadable, no need for chunking
        return signedUrl
    }

    fun uploadFromUrl(
        entityType: String,
        userId: String,
        sourceUrl: String,
    ): String {
        val downloaded = sourceUrl
            .httpGet()
            .header("User-Agent", fuelUserAgent)
            .response()
            .second

        val contentType = downloaded.header("Content-Type").firstOrNull()
        check(contentType != null) { "Downloaded URL is missing Content-Type: $sourceUrl" }

        val uploadInfo = objectName(entityType, userId, contentType, "0000")
        val blob = BlobInfo.newBuilder(BlobId.of(ASSETS_BUCKET, uploadInfo.objectName)).build()
        storage.create(blob, downloaded.data)
        return uploadInfo.publicUrl
    }
}

data class UploadInfo(
    val id: String,
    val objectName: String,
    val contentType: String,
) {
    private val bucket: String
        get() = ASSETS_BUCKET

    val publicUrl: String
        get() = "https://$bucket.storage.googleapis.com/$objectName"
}

val String.mediaType: StorageUploadsService.AssetType
    get() {
        if ("/" !in this) {
            throw BadRequestException("Media type must contain slash as for instance `image/webp`")
        }
        val (type, subtype) = this.split("/")
        return when {
            type == "image" -> IMAGE
            subtype in documentTypes -> DOCUMENT
            else -> throw BadRequestException("Unknown contentType for upload: $this")
        }
    }

private const val ASSETS_BUCKET = "heroheroco-assets"
private const val URL_EXPIRATION_IN_MINUTES = 15L
private val documentTypes = DocumentType.entries.map { it.name.lowercase() }.toSet()
