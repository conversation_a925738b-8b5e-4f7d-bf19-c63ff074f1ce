package hero.media.controller

import hero.exceptions.http.UnauthorizedException
import hero.gjirafa.GjirafaStatsService
import hero.gjirafa.dto.AddAudioWatchStateRequest
import hero.gjirafa.dto.AddVideoWatchStateRequest
import hero.gjirafa.dto.CreateAudioAnalyticsEventRequest
import hero.gjirafa.dto.CreateVideoAnalyticsEventRequest
import hero.http4k.auth.parseJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.countryCode
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.remoteAddress
import hero.media.controller.dto.exampleGjirafaAddAudioWatchStateRequest
import hero.media.controller.dto.exampleGjirafaAddVideoWatchStateRequest
import hero.media.controller.dto.exampleGjirafaCreateAudioAnalyticsEventRequest
import hero.media.controller.dto.exampleGjirafaCreateVideoAnalyticsEventRequest
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class GjirafaStatsController(
    private val gjirafaService: GjirafaStatsService,
) {
    @Suppress("Unused")
    val routePostVideoAnalyticsEvent: ContractRoute =
        ("/v1/gjirafa/video/events").post(
            summary = "Create analytics event",
            tag = "Gjirafa stats",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaCreateVideoAnalyticsEventRequest,
            handler = { request, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val createEventRequest = lens<CreateVideoAnalyticsEventRequest>(request)
                    .copy(
                        userIp = request.remoteAddress,
                        countryCode = request.countryCode,
                    )
                val response = gjirafaService.postVideoAnalyticsEvent(createEventRequest)
                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostVideoWatchState: ContractRoute =
        ("/v1/gjirafa/video/watch-states").post(
            summary = "Add watch state",
            tag = "Gjirafa stats",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaAddVideoWatchStateRequest,
            handler = { request, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val createWatchStateRequest = lens<AddVideoWatchStateRequest>(request)
                val response = gjirafaService.postVideoWatchState(createWatchStateRequest)
                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostAudioAnalyticsEvent: ContractRoute =
        ("/v1/gjirafa/audio/events").post(
            summary = "Create analytics event",
            tag = "Gjirafa stats",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaCreateAudioAnalyticsEventRequest,
            handler = { request, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val createEventRequest = lens<CreateAudioAnalyticsEventRequest>(request)
                    .copy(
                        userIp = request.remoteAddress,
                        countryCode = request.countryCode,
                    )
                val response = gjirafaService.postAudioAnalyticsEvent(createEventRequest)
                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostAudioWatchState: ContractRoute =
        ("/v1/gjirafa/audio/watch-states").post(
            summary = "Add watch state",
            tag = "Gjirafa stats",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaAddAudioWatchStateRequest,
            handler = { request, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val createWatchStateRequest = lens<AddAudioWatchStateRequest>(request)
                val response = gjirafaService.postAudioWatchState(createWatchStateRequest)
                Response(Status.OK).body(response)
            },
        )

    @Suppress("Unused")
    val routePostNewLiveVideo: ContractRoute =
        ("/v1/gjirafa/audio/watch-states").post(
            summary = "Add watch state",
            tag = "Gjirafa stats",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaAddAudioWatchStateRequest,
            handler = { request, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val createWatchStateRequest = lens<AddAudioWatchStateRequest>(request)
                val response = gjirafaService.postAudioWatchState(createWatchStateRequest)
                Response(Status.OK).body(response)
            },
        )
}
