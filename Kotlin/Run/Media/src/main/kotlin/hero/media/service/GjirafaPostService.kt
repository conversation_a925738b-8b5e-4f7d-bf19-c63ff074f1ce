package hero.media.service

import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.containsAny
import hero.gcloud.where
import hero.model.GjirafaAsset
import hero.model.GjirafaStatus
import hero.model.Post
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository

class GjirafaPostService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val pubSub: PubSub,
    private val logger: Logger = log,
) {
    fun findByAssetId(assetId: String): List<Post> =
        postsCollection
            // unfortunately we cannot query directly `assets[].*.id`, we need to duplicate ids
            // to assetIds plain array to be queried: https://stackoverflow.com/a/54081889/922584
            .where(Post::assetIds).contains(assetId)
            .and(Post::state).isIn(listOf(PostState.PROCESSING, PostState.PUBLISHED, PostState.SCHEDULED))
            .fetchAll()

    fun refreshAsset(
        post: Post,
        assetId: String,
        refreshedAsset: GjirafaAsset,
    ) {
        val originalState = post.state
        logger.info(
            "Gjirafa asset $assetId is currently ${refreshedAsset.status} " +
                "at ${refreshedAsset.progressTillReadiness}% readiness.",
            mapOf("assetId" to assetId),
        )
        post.refreshAssets(refreshedAsset)
        postRepository.save(post)
        postsCollection[post.id].field(Post::state).update(post.state)
        postsCollection[post.id].field(Post::assets).update(post.assets)
        if (post.state != originalState && post.state in setOf(PostState.PUBLISHED, PostState.SCHEDULED)) {
            pubSub.publish(PostStateChanged(PostStateChange.PUBLISHED, post))
        } else if (refreshedAsset.status in setOf(GjirafaStatus.COMPLETE, GjirafaStatus.PARTIALLY_COMPLETED)) {
            pubSub.publish(PostStateChanged(PostStateChange.PATCHED, post))
        }
    }

    fun filterUnassigned(assetIds: List<String>): List<String> {
        val assignedAssets = postsCollection.where(Post::assetIds).containsAny(assetIds)
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .fetchAll()
            .flatMap { it.assetIds }

        return assetIds.filter { it !in assignedAssets }
    }
}
