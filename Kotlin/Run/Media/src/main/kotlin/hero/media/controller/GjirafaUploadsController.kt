package hero.media.controller

import hero.baseutils.log
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.PubSub
import hero.gjirafa.GjirafaUploadsService
import hero.gjirafa.dto.exampleGjirafaAsset
import hero.http4k.auth.parseJwtUser
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.enum
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.http4k.extensions.put
import hero.jackson.fromJson
import hero.media.controller.dto.GjirafaAssetId
import hero.media.controller.dto.GjirafaEvent
import hero.media.controller.dto.GjirafaEventUploadsData
import hero.media.controller.dto.GjirafaOriginalUploadResponse
import hero.media.controller.dto.GjirafaProcessingUrl
import hero.media.controller.dto.GjirafaSubtitleAutogeneratedCompletedData
import hero.media.controller.dto.InternalGjirafaResponse
import hero.media.controller.dto.exampleGjirafaAssetId
import hero.media.controller.dto.exampleGjirafaEncodeRequest
import hero.media.controller.dto.exampleGjirafaMultipartAbort
import hero.media.controller.dto.exampleGjirafaMultipartPartsResponse
import hero.media.controller.dto.exampleGjirafaProcessingUrl
import hero.media.controller.dto.exampleGjirafaQualityType
import hero.media.controller.dto.exampleGjirafaUploadInfo
import hero.media.controller.dto.exampleGjirafaUploadResponseResult
import hero.media.service.GjirafaPostService
import hero.model.GjirafaAssetMinimal
import hero.model.GjirafaAssetType
import hero.model.GjirafaEncodeRequest
import hero.model.topics.SubtitlesGenerationCompleted
import hero.model.topics.UploadEvent
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.boolean
import org.http4k.lens.int
import org.http4k.lens.long
import org.http4k.lens.nonEmptyString
import org.http4k.lens.string
import kotlin.concurrent.thread

class GjirafaUploadsController(
    private val gjirafaService: GjirafaUploadsService,
    private val gjirafaPostService: GjirafaPostService,
    private val pubSub: PubSub,
) {
    @Suppress("unused")
    val routeGjirafaUploadsUrl: ContractRoute =
        "/v1/gjirafa-uploads".post(
            summary = "Gets a URL for a new upload.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
                val fileName = Query.nonEmptyString()
                    .required("fileName", "Original file name to be shown in UI.")
                val contentLength = Query.long()
                    .required("contentLength", "Content length of media to be uploaded.")
                val mimeType = Query.nonEmptyString()
                    .required(
                        "mimeType",
                        "Mime type of the uploaded file. " +
                            "Using `video/mp4` or `audio/m4a` should be enough in all cases.",
                    )
            },
            receiving = null,
            responses = listOf(Status.OK example exampleGjirafaUploadResponseResult),
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val fileName = parameters.fileName(request)
                val contentLength = parameters.contentLength(request)
                val mimeType = parameters.mimeType(request)
                val uploadRequest = gjirafaService.uploadsUrl(
                    userId = jwtUser.id,
                    fileName = fileName,
                    contentLength = contentLength,
                    mimeType = mimeType,
                )
                log.info("User got Gjirafa uploadId: ${uploadRequest.url}", mapOf("userId" to jwtUser.id))
                pubSub.publish(
                    UploadEvent.SinglePartUploadInitiated(
                        userId = jwtUser.id,
                        mimeType = mimeType,
                        requestKey = uploadRequest.requestKey,
                        preSignedUrl = uploadRequest.url,
                    ),
                )
                Response(Status.OK).body(
                    GjirafaProcessingUrl(
                        uploadRequest.url,
                        uploadRequest.uploadFileDetailsId,
                    ),
                )
            },
        )

    @Suppress("unused")
    val routeGjirafaUploadsMultipart: ContractRoute =
        "/v1/gjirafa-uploads-multipart".post(
            summary = "Gets a URL for a new multipart upload.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
                val fileName = Query.nonEmptyString()
                    .required("fileName", "Original file name to be shown in UI.")
                val contentLength = Query.long()
                    .required("contentLength", "Content length of media to be uploaded.")
                val mimeType = Query.nonEmptyString()
                    .required(
                        "mimeType",
                        "Mime type of the uploaded file. " +
                            "Using `video/mp4` or `audio/m4a` should be enough in all cases.",
                    )
                val partsNumbers = Query.int()
                    .required("partsNumbers", "Numbers of parts to be chunked.")
            },
            receiving = null,
            responses = listOf(Status.OK example exampleGjirafaUploadInfo),
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val fileName = parameters.fileName(request)
                val contentLength = parameters.contentLength(request)
                val mimeType = parameters.mimeType(request)
                val partsNumber = parameters.partsNumbers(request)
                val uploadRequest = gjirafaService.uploadsMultipart(
                    userId = jwtUser.id,
                    fileName = fileName,
                    contentLength = contentLength,
                    mimeType = mimeType,
                    partsNumber = partsNumber,
                )
                log.info(
                    "User got Gjirafa uploadRequest: ${uploadRequest.uploadId}",
                    mapOf(
                        "userId" to jwtUser.id,
                        "presignedUrl" to uploadRequest.presignedUrl.firstOrNull()?.presignedUrl,
                        "requestKey" to uploadRequest.requestKey,
                    ),
                )

                pubSub.publish(
                    UploadEvent.MultipartUploadInitiated(
                        mimeType = mimeType,
                        partsNumber = partsNumber,
                        contentLength = contentLength,
                        uploadId = uploadRequest.uploadId,
                        requestKey = uploadRequest.requestKey,
                        userId = jwtUser.id,
                        preSignedUrl = uploadRequest.presignedUrl.firstOrNull()?.presignedUrl,
                    ),
                )

                Response(Status.OK).body(uploadRequest)
            },
        )

    @Suppress("unused")
    val routeGjirafaEncodeUrl: ContractRoute =
        ("/v1/gjirafa-assets-encode").post(
            summary = "Start encoding process for previously uploaded Gjirafa asset.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
                val mediaType = Query.enum<GjirafaAssetType>()
                    .required("mediaType", "Type of the media to be encoded. Allowed values: [AUDIO, VIDEO]")
                val hasDrm = Query.boolean()
                    .optional("hasDrm", "Has DRM flag")
                val encodingTemplateId = Query.string()
                    .optional("encodingTemplateId", "Encoding template id")
                val uploadFileDetailsId = Query.long()
                    .optional("uploadFileDetailsId", "Upload file details identification from Gjirafa")
            },
            responses = listOf(Status.OK example exampleGjirafaAssetId),
            receiving = exampleGjirafaProcessingUrl,
            handler = { request, parameters ->
                val assetUrl = lens<GjirafaProcessingUrl>(request)
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val mediaType = parameters.mediaType(request)
                val hasDrm = parameters.hasDrm(request)
                val encodingTemplateId = parameters.encodingTemplateId(request)
                val uploadFileDetailsId = parameters.uploadFileDetailsId(request)

                val encodeResponse = runCatching {
                    gjirafaService.encodeUrl(
                        userId = jwtUser.id,
                        assetUrl = assetUrl.url,
                        assetType = mediaType,
                        hasDrm = hasDrm,
                        encodingTemplateId = encodingTemplateId,
                        uploadFileDetailsId = uploadFileDetailsId,
                    )
                }

                pubSub.publish(
                    UploadEvent.AssetEncodingInitiated(
                        userId = jwtUser.id,
                        url = assetUrl.url,
                        assetId = encodeResponse.getOrNull()?.assetId,
                        exception = encodeResponse.exceptionOrNull(),
                    ),
                )

                val assetId = encodeResponse.getOrThrow().assetId

                log.info(
                    "Gjirafa started encoding $assetId with asset url ${assetUrl.url}",
                    mapOf(
                        "assetId" to assetId,
                        "url" to assetUrl.url,
                        "userId" to jwtUser.id,
                    ),
                )

                Response(Status.OK).body(GjirafaAssetId(id = assetId))
            },
        )

    @Suppress("unused")
    val routeGjirafaCompleteMultipartUpload: ContractRoute =
        ("/v1/gjirafa-uploads-multipart-complete").post(
            summary = "Finish uploaded Gjirafa multipart asset.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example true),
            receiving = exampleGjirafaEncodeRequest,
            handler = { request, _ ->
                val encodeRequest = lens<GjirafaEncodeRequest>(request)
                val user = request.parseJwtUser() ?: throw UnauthorizedException()
                log.info(
                    "User ${user.id} is marking upload ${encodeRequest.requestKey} as complete.",
                    mapOf("userId" to user.id),
                )
                val encodeResponse = runCatching {
                    gjirafaService.completeMultipartUpload(user.id, encodeRequest)
                }

                pubSub.publish(
                    UploadEvent.MultipartUploadCompleted(
                        userId = user.id,
                        requestKey = encodeRequest.requestKey,
                        result = encodeResponse.getOrNull(),
                        exception = encodeResponse.exceptionOrNull(),
                    ),
                )

                Response(Status.OK).body(encodeResponse.getOrThrow())
            },
        )

    @Suppress("unused")
    val routeGjirafaWebhook: ContractRoute =
        ("/v1/gjirafa/uploads-webhooks").post(
            summary = "Gjirafa webhook on encoded asset.",
            tag = "Gjirafa uploads",
            parameters = object {},
            responses = listOf(Status.OK example Unit),
            receiving = exampleGjirafaQualityType,
            handler = { request, _ ->
                try {
                    val body = String(request.body.payload.array())
                    log.info("Received Gjirafa Uploads event: $body")
                    // val event = lens<GjirafaEvent>(request)
                    val event = body.fromJson<GjirafaEvent<GjirafaEventUploadsData>>()

                    // https://vp.gjirafa.tech/documentation/api/webhooks/video
                    // https://vp.gjirafa.tech/documentation/api/webhooks/audio

                    val assetId = event.data.id
                    if (assetId == null) {
                        log.fatal("Webhook was missing ID: $body")
                        return@post Response(Status.NO_CONTENT)
                    }

                    if (event.eventType == "audio.item.deleted" || event.eventType == "video.item.deleted") {
                        log.info("Asset $assetId was deleted, skipping.", mapOf("assetId" to assetId))
                        return@post Response(Status.NO_CONTENT)
                    }

                    if (event.eventType == "video.subtitle.autogenerated.completed") {
                        val subtitlesCompletedEvent = body
                            .fromJson<GjirafaEvent<GjirafaSubtitleAutogeneratedCompletedData>>()

                        pubSub.publish(
                            SubtitlesGenerationCompleted(
                                assetId = subtitlesCompletedEvent.data.videoId,
                                filePath = subtitlesCompletedEvent.data.filePath,
                            ),
                        )

                        return@post Response(Status.NO_CONTENT)
                    }

                    if (event.eventType == "video.encode.completed" || event.eventType == "audio.encode.completed") {
                        pubSub.publish(UploadEvent.AssetEncodingCompleted(assetId))
                    }

                    val posts = gjirafaPostService.findByAssetId(assetId)
                    if (posts.isEmpty()) {
                        log.warn(
                            "No posts for asset $assetId were found. Maybe not created yet.",
                            mapOf("assetId" to assetId),
                        )
                        return@post Response(Status.NO_CONTENT)
                    }

                    val refreshedAsset = gjirafaService.getAsset(
                        userId = null,
                        assetId = assetId,
                        withDebug = false,
                    )
                    posts.forEach {
                        gjirafaPostService.refreshAsset(it, assetId, refreshedAsset)
                    }
                } catch (e: Exception) {
                    log.fatal("Gjirafa Uploads webhook processing: ${e.message}, ${request.bodyString()}", cause = e)
                    throw e
                }
                Response(Status.NO_CONTENT)
            },
        )

    @Suppress("unused")
    val routeGjirafaAssets: ContractRoute =
        ("/v1/gjirafa-assets" / Path.string().of("assetId")).get(
            summary = "Get encoding progress for previously uploaded Gjirafa asset.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example exampleGjirafaAsset),
            handler = { request, _, assetId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val isAuthor = gjirafaService.isAuthor(jwtUser.id, assetId)
                val asset = gjirafaService.getAsset(userId = null, assetId = assetId, withDebug = true)
                // Eagerly refresh posts with this asset if complete progress of the asset reached 100.
                // Without this eager update, the client will get stale data from our other endpoint after
                // getting 100 from this endpoint. We want to prevent this API leakage, clients should be oblivious
                // to the fact that this is just a proxy endpoint
                if (asset.progressTillCompleteness >= 100) {
                    val posts = gjirafaPostService.findByAssetId(asset.id)
                    if (posts.isNotEmpty()) {
                        val oldAsset = posts.first().assets.mapNotNull { it.gjirafa }.first { it.id == assetId }
                        if (oldAsset.progressTillCompleteness != asset.progressTillCompleteness) {
                            posts.forEach {
                                gjirafaPostService.refreshAsset(it, assetId, asset)
                            }
                        }
                    }
                }
                Response(Status.OK).body(
                    if (isAuthor)
                        asset
                    else
                        GjirafaAssetMinimal(asset.id, asset.status),
                )
            },
        )

    @Suppress("unused")
    val routeGjirafaAssetsByAuthor: ContractRoute =
        ("/v1/gjirafa-unassigned-assets").get(
            summary = "Get unassigned assets by author.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example listOf(exampleGjirafaAsset)),
            handler = { request, _ ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val assets = gjirafaService.getAssetsByAuthor(jwtUser.id)
                val unassignedAssetIds = gjirafaPostService.filterUnassigned(assets.map { it.publicId })
                val unassignedAssets = unassignedAssetIds.map { gjirafaService.getAsset(jwtUser.id, it) }
                Response(Status.OK).body(unassignedAssets)
            },
        )

    @Suppress("unused")
    val routeGjirafaMultipartParts: ContractRoute =
        ("/v1/gjirafa-multipart" / Path.string().of("requestKey") / "parts").get(
            summary = "Get multipart parts",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example exampleGjirafaMultipartPartsResponse),
            handler = { request, _, requestKey, _ ->
                request.parseJwtUser() ?: throw UnauthorizedException()
                val body = InternalGjirafaResponse(gjirafaService.getParts(requestKey))
                Response(Status.OK).body(body)
            },
        )

    @Suppress("unused")
    val routeGjirafaMultipartAbort: ContractRoute =
        ("/v1/gjirafa-multipart-abort" / Path.string().of("requestKey")).put(
            summary = "Get multipart parts",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
                val skipGjirafaAbort = Query.boolean().defaulted("skip-gjirafa", true)
            },
            receiving = null,
            responses = listOf(Status.OK example exampleGjirafaMultipartAbort),
            handler = { request, parameters, requestKey ->
                val skipGjirafaAbort = parameters.skipGjirafaAbort(request)
                val user = request.parseJwtUser() ?: throw UnauthorizedException()
                val body = runCatching {
                    // clients never want to actually abort because then they cannot resume the upload
                    if (skipGjirafaAbort) {
                        InternalGjirafaResponse(true)
                    } else {
                        InternalGjirafaResponse(gjirafaService.abortMultipart(requestKey))
                    }
                }
                log.info(
                    "User ${user.id} is aborting upload $requestKey.",
                    mapOf("userId" to user.id),
                )

                pubSub.publish(
                    UploadEvent.MultipartUploadAborted(
                        userId = user.id,
                        requestKey = requestKey,
                        result = body.getOrNull()?.result,
                        exception = body.exceptionOrNull(),
                    ),
                )

                Response(Status.OK).body(body.getOrThrow())
            },
        )

    @Suppress("unused")
    val routeOriginalAsset: ContractRoute =
        ("/v1/original-asset/" / Path.string().of("assetId")).post(
            summary = "Generates a link for the originally uploaded asset.",
            tag = "Gjirafa uploads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(
                Status.OK example GjirafaOriginalUploadResponse("https://video.url"),
                Status.GONE example Unit,
            ),
            receiving = null,
            handler = { request, _, assetId ->
                val jwtUser = request.parseJwtUser() ?: throw UnauthorizedException()
                val asset = gjirafaService.getAsset(userId = jwtUser.id, assetId = assetId, withDebug = true)
                log.info("User ${jwtUser.id} is requesting original file for $assetId.", mapOf("assetId" to assetId))
                if (!asset.hasVideo) {
                    // for audios, we return directly static url
                    val audioUrl = asset.audioStaticUrl ?: error("Audio $assetId had `null` static url.")
                    return@post Response(Status.OK).body(GjirafaOriginalUploadResponse(audioUrl))
                }
                thread {
                    // This may potentially take a long time and we don't need to have this for generating
                    // the download link as it is only needed when user starts the download. Therefore
                    // it is convinient to run it in background.
                    try {
                        gjirafaService.setPermissions(2, assetId)
                    } catch (e: Exception) {
                        log.warn("Could not set permissions for download of $assetId.", mapOf("assetId" to assetId))
                    }
                }
                val uploadId = asset.debugDetail!!.originalFile!!.replace("[?].*".toRegex(), "")
                val uploadUrl = if (uploadId.startsWith("https://")) {
                    uploadId
                } else {
                    val upload = gjirafaService.getUpload(uploadId)
                    upload.result.filePath
                }
                Response(Status.OK).body(GjirafaOriginalUploadResponse(uploadUrl))
            },
        )
}
