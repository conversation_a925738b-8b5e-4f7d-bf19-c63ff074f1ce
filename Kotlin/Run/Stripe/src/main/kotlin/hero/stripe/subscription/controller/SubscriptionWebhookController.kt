package hero.stripe.subscription.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import hero.stripe.subscription.service.ProcessSubscription
import hero.stripe.subscription.service.SubscriptionCommandService
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class SubscriptionWebhookController(
    private val subscriptionCommandService: SubscriptionCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeWebhookSubscriptions: ContractRoute =
        "/v1/webhooks/subscriptions".post(
            summary = "Handle Stripe subscription events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.subscriptions)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for subscriptions: : ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "subscription") {
                    throw BadRequestException("Stripe event was not for `subscription`: ${payload.objectType}")
                }
                val subscriptionId = payload.objectId
                    ?: throw BadRequestException("Field objectId was not given.", mapOf())

                subscriptionCommandService.execute(ProcessSubscription(subscriptionId, currency))

                Response(Status.NO_CONTENT)
            },
        )
}
