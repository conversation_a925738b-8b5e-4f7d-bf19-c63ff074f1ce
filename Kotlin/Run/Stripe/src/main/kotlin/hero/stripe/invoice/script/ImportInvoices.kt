package hero.stripe.invoice.script

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.github.doyaaaaaken.kotlincsv.dsl.csvReader
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.StripeInvoiceRecord
import hero.stripe.util.clean
import hero.stripe.util.importData
import hero.stripe.util.objectMapper
import hero.stripe.util.sanitizeBrokenJsonQuotes
import org.jooq.DSLContext
import org.jooq.JSONB
import org.jooq.impl.DSL.excluded
import java.io.File
import java.time.Instant

fun main() {
    importData(
        finishedFileName = "finished-invoices.txt",
        dataFolder = "/Users/<USER>/Downloads/invoices",
        processFile = ::processFile,
    )
}

fun processFile(
    file: File,
    context: DSLContext,
) {
    csvReader().readAllWithHeader(file)
        .map {
            StripeInvoiceRecord().apply {
                this.id = it["id"]
                this.accountName = it["account_name"]!!
                this.accountCountry = it["account_country"]!!
                this.collectionMethod = it["collection_method"]!!
                this.currency = it["currency"]!!
                this.customerId = it["customer"]!!
                this.subscriptionId = it["subscription"]!!
                this.description = it["description"]!!
                val linesData = if (it["lines"]!!.isBlank() == true) {
                    null
                } else {
                    val lines = it["lines"]!!.clean()

                    val content = sanitizeBrokenJsonQuotes(lines)
                    objectMapper.readTree(content)
                        .get("data")
                        .let {
                            objectMapper.writeValueAsString(it)
                        }
                }
                this.linesData = JSONB.valueOf(linesData)
                val metadata = if (it["metadata"]?.isBlank() == true) {
                    "{}"
                } else {
                    it["metadata"]
                }

                val discountValue = it["discount"]!!.clean()
                val discount = if (discountValue.isBlank() == true) {
                    null
                } else {
                    objectMapper.readValue(
                        sanitizeBrokenJsonQuotes(discountValue),
                        Discount::class.java,
                    )
                }

                this.metadata = JSONB.valueOf(metadata)
                this.periodStart = Instant.ofEpochSecond(it["period_start"]!!.toLong())
                this.periodEnd = Instant.ofEpochSecond(it["period_end"]!!.toLong())
                this.status = it["status"]!!
                this.number = it["number"]!!
                this.onBehalfOf = it["on_behalf_of"]!!
                this.billingReason = it["billing_reason"]!!
                this.discounts = if (discount != null) arrayOf(discount.coupon!!.id) else arrayOf()
                this.createdAt = Instant.ofEpochSecond(it["created"]!!.toLong())
                this.total = it["total"]!!.toInt()
                this.paymentIntentId = it["payment_intent"]!!
                this.transferData = if (it["transfer_data"]!!.isBlank() == true) {
                    null
                } else {
                    JSONB.valueOf(it["transfer_data"]!!.clean())
                }
            }
        }
        .chunked(1000)
        .forEachIndexed { index, chunk ->
            context
                .insertInto(Tables.STRIPE_INVOICE)
                .set(chunk)
                .onDuplicateKeyUpdate()
                .set(Tables.STRIPE_INVOICE.ACCOUNT_NAME, excluded(Tables.STRIPE_INVOICE.ACCOUNT_NAME))
                .set(Tables.STRIPE_INVOICE.ACCOUNT_COUNTRY, excluded(Tables.STRIPE_INVOICE.ACCOUNT_COUNTRY))
                .set(Tables.STRIPE_INVOICE.COLLECTION_METHOD, excluded(Tables.STRIPE_INVOICE.COLLECTION_METHOD))
                .set(Tables.STRIPE_INVOICE.CURRENCY, excluded(Tables.STRIPE_INVOICE.CURRENCY))
                .set(Tables.STRIPE_INVOICE.CUSTOMER_ID, excluded(Tables.STRIPE_INVOICE.CUSTOMER_ID))
                .set(Tables.STRIPE_INVOICE.SUBSCRIPTION_ID, excluded(Tables.STRIPE_INVOICE.SUBSCRIPTION_ID))
                .set(Tables.STRIPE_INVOICE.DESCRIPTION, excluded(Tables.STRIPE_INVOICE.DESCRIPTION))
                .set(Tables.STRIPE_INVOICE.LINES_DATA, excluded(Tables.STRIPE_INVOICE.LINES_DATA))
                .set(Tables.STRIPE_INVOICE.METADATA, excluded(Tables.STRIPE_INVOICE.METADATA))
                .set(Tables.STRIPE_INVOICE.PERIOD_START, excluded(Tables.STRIPE_INVOICE.PERIOD_START))
                .set(Tables.STRIPE_INVOICE.PERIOD_END, excluded(Tables.STRIPE_INVOICE.PERIOD_END))
                .set(Tables.STRIPE_INVOICE.STATUS, excluded(Tables.STRIPE_INVOICE.STATUS))
                .set(Tables.STRIPE_INVOICE.NUMBER, excluded(Tables.STRIPE_INVOICE.NUMBER))
                .set(Tables.STRIPE_INVOICE.ON_BEHALF_OF, excluded(Tables.STRIPE_INVOICE.ON_BEHALF_OF))
                .set(Tables.STRIPE_INVOICE.BILLING_REASON, excluded(Tables.STRIPE_INVOICE.BILLING_REASON))
                .set(Tables.STRIPE_INVOICE.DISCOUNTS, excluded(Tables.STRIPE_INVOICE.DISCOUNTS))
                .set(Tables.STRIPE_INVOICE.CREATED_AT, excluded(Tables.STRIPE_INVOICE.CREATED_AT))
                .set(Tables.STRIPE_INVOICE.TOTAL, excluded(Tables.STRIPE_INVOICE.TOTAL))
                .set(Tables.STRIPE_INVOICE.PAYMENT_INTENT_ID, excluded(Tables.STRIPE_INVOICE.PAYMENT_INTENT_ID))
                .set(Tables.STRIPE_INVOICE.TRANSFER_DATA, excluded(Tables.STRIPE_INVOICE.TRANSFER_DATA))
                .execute()
        }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class Discount(val coupon: Coupon? = null)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Coupon(val id: String? = null)
