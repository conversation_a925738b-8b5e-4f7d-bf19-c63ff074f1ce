package hero.stripe.charge

import com.stripe.param.ChargeRetrieveParams
import hero.baseutils.log
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.root
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import hero.sql.jooq.tables.Charge.CHARGE
import hero.stripe.service.StripeClients
import org.jooq.DSLContext
import org.jooq.JSONB
import java.time.Instant

class ChargeCommandService(
    lazyContext: Lazy<DSLContext>,
    private val stripeClients: StripeClients,
    private val usersCollection: TypedCollectionReference<User>,
) {
    private val context by lazyContext

    fun execute(command: ProcessCharge) {
        val charge = stripeClients[command.currency]
            .charges()
            .retrieve(
                command.chargeId,
                ChargeRetrieveParams.builder()
                    .addAllExpand(
                        listOf(
                            "invoice.subscription",
                            "transfer.destination_payment",
                            "payment_intent.payment_method",
                        ),
                    )
                    .build(),
            )

        val paymentMethod = stripeClients[charge.currency].paymentMethods().retrieve(charge.paymentMethod)

        if (charge.customer == null) {
            log.warn("Charge ${charge.id} is missing customer id, not storing in SQL")
            return
        }

        if (charge.transferData?.destination == null) {
            log.warn("Missing destination account id for charge ${charge.id}, not storing in SQL")
            return
        }

        val destination = charge.transferData.destination
        val creator = usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(destination)
            .fetchSingle()
            ?: usersCollection
                .where(root(User::creator).path(Creator::stripeAccountLegacyIds)).contains(destination)
                .fetchSingle()

        if (creator == null) {
            log.warn("Failed to find creator for charge ${charge.id}")
        }

        val payoutId = charge.transferObject?.destinationPaymentObject?.metadata?.get("payoutId")
        context
            .insertInto(CHARGE)
            .set(CHARGE.STRIPE_ID, charge.id)
            .set(CHARGE.INVOICE_ID, charge.invoice)
            .set(CHARGE.CUSTOMER_ID, charge.customer)
            .set(CHARGE.TRANSFER_AMOUNT, charge.transferData?.amount?.toInt())
            .set(CHARGE.TRANSFER_DESTINATION, destination)
            .set(CHARGE.CREATOR_ID, creator?.id)
            .set(CHARGE.AMOUNT, charge.amount)
            .set(CHARGE.AMOUNT_CAPTURED, charge.amountCaptured)
            .set(CHARGE.AMOUNT_REFUNDED, charge.amountRefunded)
            .set(CHARGE.PAYMENT_INTENT_ID, charge.paymentIntent)
            .set(CHARGE.STATUS, charge.status)
            .set(CHARGE.STRIPE_CREATED_AT, Instant.ofEpochSecond(charge.created))
            .set(CHARGE.PAYMENT_METHOD_ID, charge.paymentMethod)
            .set(CHARGE.CURRENCY, charge.currency.uppercase())
            .set(CHARGE.REFUNDED, charge.refunded)
            .set(CHARGE.DISPUTED, charge.disputed)
            .set(CHARGE.DESCRIPTION, charge.description)
            .set(CHARGE.PAYMENT_METHOD_TYPE, paymentMethod.metadata["cardCreateType"])
            .set(CHARGE.CARD_BRAND, charge.paymentMethodDetails.card?.brand)
            .set(CHARGE.CARD_COUNTRY, charge.paymentMethodDetails.card?.country)
            .set(CHARGE.CARD3DS, charge.paymentMethodDetails.card?.threeDSecure?.result)
            .set(CHARGE.CARD_CVC_CHECK, charge.paymentMethodDetails.card?.checks?.cvcCheck)
            .set(CHARGE.FAILURE_CODE, charge.failureCode)
            .set(CHARGE.FAILURE_MESSAGE, charge.failureMessage)
            .set(CHARGE.CARD_LAST_4, charge.paymentMethodDetails.card?.last4)
            .set(CHARGE.TRANSFER, charge.transferObject?.let { JSONB.valueOf(it.toJson()) })
            .set(CHARGE.PAYOUT_ID, payoutId)
            .set(CHARGE.CREATED_AT, Instant.ofEpochSecond(charge.created))
            .set(CHARGE.METADATA, JSONB.valueOf(charge.metadata.toJson()))
            .onConflict(CHARGE.STRIPE_ID)
            .doUpdate()
            .set(CHARGE.STATUS, charge.status)
            .set(CHARGE.INVOICE_ID, charge.invoice)
            .set(CHARGE.TRANSFER_AMOUNT, charge.transferData?.amount?.toInt())
            .set(CHARGE.TRANSFER_DESTINATION, destination)
            .set(CHARGE.CREATOR_ID, creator?.id)
            .set(CHARGE.AMOUNT_CAPTURED, charge.amountCaptured)
            .set(CHARGE.AMOUNT_REFUNDED, charge.amountRefunded)
            .set(CHARGE.REFUNDED, charge.refunded)
            .set(CHARGE.DISPUTED, charge.disputed)
            .set(CHARGE.FAILURE_CODE, charge.failureCode)
            .set(CHARGE.FAILURE_MESSAGE, charge.failureMessage)
            .set(CHARGE.CARD_LAST_4, charge.paymentMethodDetails.card?.last4)
            .set(CHARGE.TRANSFER, charge.transferObject?.let { JSONB.valueOf(it.toJson()) })
            .set(CHARGE.PAYOUT_ID, payoutId)
            .set(CHARGE.CREATED_AT, Instant.ofEpochSecond(charge.created))
            .set(CHARGE.METADATA, JSONB.valueOf(charge.metadata.toJson()))
            .execute()
    }
}

data class ProcessCharge(val chargeId: String, val currency: Currency)
