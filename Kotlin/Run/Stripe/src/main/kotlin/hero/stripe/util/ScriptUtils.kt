package hero.stripe.util

import com.fasterxml.jackson.databind.ObjectMapper
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import java.io.File
import java.util.concurrent.atomic.AtomicInteger

fun importData(
    finishedFileName: String,
    dataFolder: String,
    processFile: (File, DSLContext) -> Unit,
    context: DSLContext = JooqSQL.context(ConnectorConnectionPool.dataSource),
) {
    val finishedFile = File(finishedFileName)
    if (!finishedFile.exists()) {
        finishedFile.createNewFile()
    }
    val finishedFiles = finishedFile.readLines().toMutableSet()

    val files = File(dataFolder)
        .walk()
        .filter { it.isFile }
        .filter { "csv" in it.name }
        .filter { it.name !in finishedFiles }
        .toList()

    val doneCount = AtomicInteger(0)
    println("Found ${files.size} files")

    runBlocking(Dispatchers.Default) {
        files
            .filter {
                it.name !in finishedFiles
            }
            .map { file ->
                async {
                    var attempts = 0
                    while (true) {
                        if (attempts > 5) {
                            println("Failed processing ${file.name}")
                            break
                        }
                        try {
                            processFile(file, context)
                            println("Done processing ${file.name}")
                            val f = doneCount.incrementAndGet()
                            if (f % 10 == 0) {
                                println("Finished $f out of ${files.size}")
                            }
                            finishedFile.appendText("${file.name}\n")
                            break
                        } catch (e: Exception) {
                            attempts++
                            println("Error processing ${file.name}: ${e.message}")
                        }
                    }
                }
            }
            .awaitAll()
    }
}

fun String.clean() =
    this
        .replace("'", "\"")
        .replace("True", "true")
        .replace("False", "false")
        .replace("\\", "")
        .replace("None", "null")

fun sanitizeBrokenJsonQuotes(input: String): String {
    val output = StringBuilder()
    var insideString = false

    input.forEachIndexed { index, char ->
        if (char == '"') {
            if (!insideString) {
                insideString = true
                output.append(char)
            } else {
                var i = index + 1
                var nextToken: Char? = null
                while (i < input.length) {
                    if (!(input[i].isWhitespace())) {
                        nextToken = input[i]
                        break
                    }
                    i++
                }

                if (nextToken != ':' && nextToken != '}' && nextToken != ',' && nextToken != ']') {
                    output.append('\\')
                    output.append(char)
                } else {
                    output.append(char)
                    insideString = false
                }
            }
        } else {
            output.append(char)
        }
    }

    return output.toString()
}

val objectMapper = ObjectMapper()
