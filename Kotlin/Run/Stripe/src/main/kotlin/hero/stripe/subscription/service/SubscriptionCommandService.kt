package hero.stripe.subscription.service

import com.stripe.exception.InvalidRequestException
import com.stripe.exception.StripeException
import com.stripe.model.Coupon
import com.stripe.model.Subscription
import com.stripe.net.RequestOptions
import com.stripe.param.CouponCreateParams
import com.stripe.param.InvoiceRetrieveParams
import hero.baseutils.instantOf
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.nullIfEmpty
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ServerException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.entry
import hero.gcloud.root
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Currency
import hero.model.Subscriber
import hero.model.User
import hero.model.topics.CouponApplied
import hero.model.topics.SubscriberChanged
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.stripe.coupon.service.CouponCommandService
import hero.stripe.coupon.service.ProcessCoupon
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.stripeRetry
import org.jooq.DSLContext
import org.jooq.JSONB
import java.time.Duration
import java.time.Instant

class SubscriptionCommandService(
    private val stripeService: StripeService,
    private val usersCollection: TypedCollectionReference<User>,
    private val couponCommandService: CouponCommandService,
    private val pubSub: PubSub,
    private val subscriptionService: StripeSubscriptionService,
    private val stripeClients: StripeClients,
    private val stripeSubscriberSaver: StripeSubscriberSaver,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(command: ProcessSubscription) {
        val subscription = stripeService.subscription(command.subscriptionId, command.currency)
        if (subscription.metadata["ignore"] == "true") {
            // ignoring bad results of migration
            return
        }
        val logMap = mapOf(
            "subscriptionId" to subscription.id,
            "userId" to subscription.metadata["userId"],
            "creatorId" to subscription.metadata["creatorId"],
            "appleReferenceId" to subscription.metadata["appleReferenceId"],
        )

        val user = subscription.metadata["userId"]?.let { usersCollection[it].fetch() }
            ?: findByCustomerId(subscription.customer)
            ?: error("User not found for customer ${subscription.customer}")
        writeToSQL(subscription, user.id)
        try {
            processSubscription(user, subscription.customer, command.currency)
        } catch (e: InvalidRequestException) {
            throw ServerException(
                "Cannot process subscription ${user.id}/${command.subscriptionId}: ${e.message}",
                logMap,
                e,
            )
        }

        val coupon = subscription.discount?.coupon
        if (coupon != null) {
            if (subscription.status in setOf("incomplete", "incomplete_expired")) {
                restoreCoupon(coupon, command.currency, subscription.id)
            }
            couponCommandService.execute(ProcessCoupon(coupon.id, command.currency))
            pubSub.publish(CouponApplied(subscriptionId = subscription.id, currency = command.currency))
        }

        val userId = subscription.metadata["userId"].nullIfEmpty()
            // old subscriptions do not contain userId in their meta
            ?: userIdByCustomerAndCurrency(subscription.customer, subscription.currency.uppercase())
            ?: error("Cannot find User for ${subscription.customer} and currency ${subscription.currency}.")

        if (subscription.status == "past_due") {
            // https://linear.app/herohero/issue/HH-1902
            // This is the only place where we need to initiate UI notifications about failed payments as
            // these will come from automatic charges with no user interaction.
            usersCollection[userId].field(User::lastChargeFailedAt).update(Instant.now())
        } else {
            usersCollection[userId].field(User::lastChargeFailedAt).update(null)
        }

        pubSub.publish(
            SubscriberChanged(
                userId = userId,
                creatorId = subscription.metadata["creatorId"] ?: error("Missing creatorId"),
                doNotNotify = subscription.metadata["doNotNotify"] == "true",
            ),
        )

        if (subscription.status == "created" && logMap["appleReferenceId"] != null) {
            val sinceCreated = Duration.between(subscription.createdAt, Instant.now())
                .toMillis().toBigDecimal().movePointLeft(3)
            log.fatal("Subscription ${subscription.id} for $userId created $sinceCreated s ago.", logMap)
        }
        log.info("Wrote subscription ${subscription.id} for $userId to PostgreSQL", logMap)
    }

    private fun processSubscription(
        user: User,
        stripeCustomerId: String,
        currency: Currency,
    ) {
        log.info("Subscription state changed for ${user.id}.", mapOf("userId" to user.id))

        if (!user.isStripeCustomer) {
            throw BadRequestException("User is not a Stripe customer.", mapOf("userId" to user.id))
        }

        val subscriptions = subscriptionService.getSubscriptionsByCustomer(
            stripeCustomerId,
            null,
            filterActive = false,
            currency,
        )

        subscriptions
            .filter { it.items.data.size != 1 }
            .onEach {
                log.fatal(
                    "Subscription didn't contain exactly 1 item.",
                    mapOf("userId" to user.id, "subscription" to it.id, "items" to it.items.data.size),
                )
            }

        // Ideally this should be reworked to handle subscription by their ID (as tried in 22293590a732198550b2788e818c3a7d0388a43e)
        // however this prooved dangerous as it randomly expired current subscriptions. We should store stripeSubscriptionId to overcome this.
        val subscriptionMap = flattenSubscriptions(subscriptions)

        log.info(
            "User currently has ${subscriptionMap.size} subscriptions.",
            mapOf("userId" to user.id),
        )

        subscriptionMap.forEach { (creatorId, subscription) ->
            val invoice = if (subscription.status == "canceled" && subscription.latestInvoice != null)
                stripeClients[subscription.currency].invoices().retrieve(
                    subscription.latestInvoice,
                    InvoiceRetrieveParams.builder().addAllExpand(listOf("charge")).build(),
                    RequestOptions.getDefault(),
                )
            else
                null

            val message = listOfNotNull(
                subscription.cancellationDetails.reason,
                subscription.cancellationDetails.comment,
                subscription.cancellationDetails.feedback,
                invoice?.chargeObject?.failureMessage,
            ).joinToString(", ")

            val (refunded: Boolean, refused: Boolean) = when {
                subscription.status == "canceled" && invoice?.chargeObject == null -> {
                    (false to false)
                }

                subscription.status == "canceled" && invoice != null -> {
                    // fetch stripe last invoice and its charge
                    val charge = invoice.chargeObject
                    // subscription is refunded if the charged amount is the same as refunded amount
                    ((charge.amount == charge.amountRefunded) to !invoice.paid)
                }

                else -> {
                    (false to false)
                }
            }
            // unfortunately we cannot update cancelled subscriptions, Stripe fails with:
            // You cannot update a subscription that is `canceled` or `incomplete_expired`.
            // subscription.update(SubscriptionUpdateParams.builder().setMetadata(mapOf("refunded" to refunded.toString())).build())
            subscription.metadata[Subscriber::refunded.name] = refunded.toString()
            subscription.metadata[Subscriber::refused.name] = refused.toString()
            subscription.metadata[Subscriber::cancelledReason.name] = message
            stripeSubscriberSaver.save(user.id, creatorId, subscription, false)
        }
    }

    private fun writeToSQL(
        subscription: Subscription,
        userId: String,
    ) {
        val couponId = subscription.discount?.coupon?.id
        context
            .insertInto(
                SUBSCRIPTION,
                SUBSCRIPTION.STRIPE_ID,
                SUBSCRIPTION.STATUS,
                SUBSCRIPTION.CUSTOMER_ID,
                SUBSCRIPTION.STARTED_AT,
                SUBSCRIPTION.ENDS_AT,
                SUBSCRIPTION.ENDED_AT,
                SUBSCRIPTION.CURRENCY,
                SUBSCRIPTION.PRICE_CENTS,
                SUBSCRIPTION.CREATOR_ID,
                SUBSCRIPTION.CREATOR_COUNTRY,
                SUBSCRIPTION.TIER_ID,
                SUBSCRIPTION.CANCELLED_AT,
                SUBSCRIPTION.CANCELLATION_REASON,
                SUBSCRIPTION.COUPON_METHOD,
                SUBSCRIPTION.COUPON_PERCENT_OFF,
                SUBSCRIPTION.COUPON_EXPIRES_AT,
                SUBSCRIPTION.USER_ID,
                SUBSCRIPTION.METADATA,
                SUBSCRIPTION.COUPON_ID,
            ).values(
                subscription.id,
                subscription.status,
                subscription.customer,
                subscription.createdAt,
                subscription.endsAt,
                subscription.endedAtInstant,
                subscription.currency.uppercase(),
                subscription.priceCents,
                subscription.metadata["creatorId"],
                subscription.metadata["creatorCountry"],
                subscription.metadata["tierId"],
                subscription.cancelledAt,
                subscription.cancellationDetails?.reason,
                subscription.couponMethod,
                subscription.couponPercentOff,
                subscription.couponExpiresAt,
                userId,
                JSONB.valueOf(subscription.metadata.toJson()),
                couponId,
            ).onConflict(SUBSCRIPTION.STRIPE_ID)
            .doUpdate()
            .set(SUBSCRIPTION.ENDS_AT, subscription.endsAt)
            .set(SUBSCRIPTION.ENDED_AT, subscription.endedAtInstant)
            .set(SUBSCRIPTION.CANCELLED_AT, subscription.cancelledAt)
            .set(SUBSCRIPTION.CANCELLATION_REASON, subscription.cancellationDetails?.reason)
            .set(SUBSCRIPTION.STATUS, subscription.status)
            .set(SUBSCRIPTION.TIER_ID, subscription.metadata["tierId"])
            .set(SUBSCRIPTION.PRICE_CENTS, subscription.priceCents)
            .set(SUBSCRIPTION.UPDATED_AT, Instant.now())
            .set(SUBSCRIPTION.COUPON_METHOD, subscription.couponMethod)
            .set(SUBSCRIPTION.COUPON_PERCENT_OFF, subscription.couponPercentOff)
            .set(SUBSCRIPTION.COUPON_EXPIRES_AT, subscription.couponExpiresAt)
            .set(SUBSCRIPTION.METADATA, JSONB.valueOf(subscription.metadata.toJson()))
            .let {
                if (couponId != null) {
                    it.set(SUBSCRIPTION.COUPON_ID, couponId)
                } else {
                    it
                }
            }
            .execute()
    }

    private fun userIdByCustomerAndCurrency(
        customerId: String,
        currency: String,
    ): String? =
        usersCollection
            .where(root(User::customerIds).entry(currency))
            .isEqualTo(customerId)
            .fetchSingle()
            ?.id

    private fun findByCustomerId(customerId: String): User? =
        Currency.entries
            .firstNotNullOfOrNull {
                usersCollection.where(root(User::customerIds).entry(it.name)).isEqualTo(customerId).fetchSingle()
            }

    internal fun restoreCoupon(
        coupon: Coupon,
        currency: Currency,
        subscriptionId: String,
    ): Coupon? {
        // this concerns only unique non-100% coupons which must be additionally paid
        // and the payment just failed
        if (coupon.maxRedemptions != 1L || coupon.percentOff == 100.toBigDecimal()) {
            return null
        }
        log.info(
            "Recreating coupon ${coupon.id} in $currency as its try for application failed (see HH-4625).",
            mapOf("couponId" to coupon.id),
        )
        try {
            stripeRetry { stripeClients[currency].coupons().delete(coupon.id) }
            context.update(SUBSCRIPTION)
                .setNull(SUBSCRIPTION.COUPON_ID)
                .where(SUBSCRIPTION.STRIPE_ID.eq(subscriptionId))
                .and(SUBSCRIPTION.COUPON_ID.eq(coupon.id))
                .execute()
        } catch (e: StripeException) {
            // we don't care about failures here
            log.error("Cannot delete coupon ${coupon.id}: ${e.message}", mapOf("couponId" to coupon.id))
        }
        return stripeRetry {
            val couponsService = stripeClients[currency].coupons()
            couponsService.create(
                CouponCreateParams.builder()
                    .setId(coupon.id)
                    .setDuration(CouponCreateParams.Duration.valueOf(coupon.duration.uppercase()))
                    .setDurationInMonths(coupon.durationInMonths)
                    .setMetadata(
                        coupon.metadata + mapOf(
                            "recreatedAt" to Instant.now().toString(),
                            "recreatedCount" to
                                (coupon.metadata["recreatedCount"]?.toIntOrNull()?.plus(1) ?: 1).toString(),
                        ),
                    )
                    .setMaxRedemptions(coupon.maxRedemptions)
                    .setRedeemBy(coupon.redeemBy)
                    .setAppliesTo(
                        coupon.appliesTo?.let {
                            CouponCreateParams.AppliesTo.builder().addAllProduct(it.products).build()
                        },
                    )
                    .setPercentOff(coupon.percentOff)
                    .build(),
            )
        }
    }
}

private val Subscription.priceCents
    get() = items
        .autoPagingIterable()
        .firstOrNull()
        ?.price
        ?.unitAmount

private val Subscription.endsAt
    get() = Instant.ofEpochSecond(currentPeriodEnd)

private val Subscription.createdAt
    get() = Instant.ofEpochSecond(created)

private val Subscription.cancelledAt
    get() = canceledAt?.let { Instant.ofEpochSecond(it) }

private val Subscription.endedAtInstant
    get() = endedAt?.let { Instant.ofEpochSecond(it) }

private val Subscription.couponMethod
    get() = metadata[Subscriber::couponMethod.name]

private val Subscription.couponPercentOff
    get() = metadata[Subscriber::couponPercentOff.name]?.toIntOrNull()

private val Subscription.couponExpiresAt
    get() = metadata[Subscriber::couponExpiresAt.name]?.let { instantOf(it) }

internal fun flattenSubscriptions(subscriptions: Sequence<Subscription>): Map<String, Subscription> =
    subscriptions
        // avoid re-processing old subscriptions - note that cancelled subscriptions should always have
        // cancelAt non-null - which unfortunately does not happen sometimes
        // be warned: `cancelAt` is the time when the subscription is set to end.
        // subscription must either be active or ended only recently
        // for example sub_1M8gIzB6ZCHekl2RWaV4ac6Y has cancelAt null but the subscription has ended in 2022
        .filter {
            val isActiveOrToBeCancelled = it.status in setOf("active", "past_due") ||
                (it.cancelAt ?: Long.MAX_VALUE) > Instant.now().minusDays(7).epochSecond
            val hasNotEndedOutsideAllowedPeriod = it.endedAt == null ||
                it.endedAt > Instant.now().minusDays(7).epochSecond

            isActiveOrToBeCancelled && hasNotEndedOutsideAllowedPeriod
        }
        .mapNotNull { subscription ->
            val creatorId = subscription.metadata["creatorId"]
            if (creatorId == null) {
                log.fatal("Field creatorId was missing in ${subscription.id} metadata: ${subscription.metadata}")
                return@mapNotNull null
            }
            (creatorId to subscription)
        }
        // we group to { creatorId -> [listOfSubscriptions] }
        .groupBy({ it.first }, { it.second })
        // then we extract the latest subscription
        .map { (creatorId, subscriptionGroup) ->
            creatorId to subscriptionGroup.let {
                it.filter { it.status == "active" }.ifEmpty { null }
                    ?: it.filter { it.status == "past_due" }.ifEmpty { null }
                    // all the other statuses we consider as inactive
                    ?: it
            }
        }
        .map { (creatorId, subscriptionGroup) ->
            try {
                creatorId to subscriptionGroup.maxByOrNull { it.currentPeriodEnd ?: 0 }!!
            } catch (e: Exception) {
                @Suppress("ktlint:standard:max-line-length")
                log.fatal(
                    "$creatorId, $subscriptionGroup, ${subscriptionGroup.firstOrNull()?.currentPeriodEnd} ${e.message}",
                )
                throw e
            }
        }
        .toMap()

data class ProcessSubscription(
    val subscriptionId: String,
    val currency: Currency,
)
