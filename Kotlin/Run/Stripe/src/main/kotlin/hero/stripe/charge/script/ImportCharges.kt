package hero.stripe.charge.script

import com.github.doyaaaaaken.kotlincsv.dsl.csvReader
import hero.model.Creator
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.ChargeRecord
import hero.stripe.util.clean
import hero.stripe.util.importData
import hero.stripe.util.objectMapper
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.excluded
import java.io.File
import java.time.Instant

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val stripeAccountIdField = DSL.jsonbGetAttributeAsText(Tables.USER.CREATOR, Creator::stripeAccountId.name)
    val usersByStripeAccountId = context
        .select(stripeAccountIdField, Tables.USER.ID)
        .from(Tables.USER)
        .where(stripeAccountIdField.isNotNull)
        .fetch()
        .associate { it[stripeAccountIdField] to it[Tables.USER.ID] }

    importData(
        finishedFileName = "finished-charges.txt",
        dataFolder = "/Users/<USER>/Downloads/charges",
        processFile = { file, context -> processFile(file, context, usersByStripeAccountId) },
        context = context,
    )
}

fun processFile(
    file: File,
    context: DSLContext,
    usersByStripeAccountId: Map<String, String>,
) {
    csvReader().readAllWithHeader(file)
        .map {
            val transferData = it["transfer_data"]!!.clean().let {
                if (it.isBlank()) {
                    null
                } else {
                    objectMapper.readValue(it, TransferData::class.java)
                }
            }
            val string = usersByStripeAccountId[transferData?.destination]
            ChargeRecord().apply {
                this.stripeId = it["id"]!!
                this.customerId = it["customer"]!!
                this.amount = it["amount"]!!.toLong()
                this.amountCaptured = it["amount_captured"]!!.toLong()
                this.amountRefunded = it["amount_refunded"]!!.toLong()
                this.paymentIntentId = it["payment_intent"]!!
                this.status = it["status"]!!
                this.stripeCreatedAt = Instant.ofEpochSecond(it["created"]!!.toLong())
                this.paymentMethodId = it["payment_method"]!!
                this.succeededCharges = -1
                this.currency = it["currency"]!!
                this.refunded = it["refunded"]!!.toBoolean()
                this.disputed = it["disputed"]!!.toBoolean()
                this.description = it["description"]!!
                this.failureCode = it["failure_code"]!!
                this.failureMessage = it["failure_message"]!!
                this.transferAmount = transferData?.amount?.toInt()
                this.transferDestination = transferData?.destination
                this.creatorId = string
                this.invoiceId = it["invoice"]!!
            }
        }
        .chunked(1000)
        .forEachIndexed { index, chunk ->
            context
                .insertInto(Tables.CHARGE)
                .set(chunk)
                .onDuplicateKeyUpdate()
                .set(Tables.CHARGE.INVOICE_ID, excluded(Tables.CHARGE.INVOICE_ID))
                .set(Tables.CHARGE.CREATOR_ID, excluded(Tables.CHARGE.CREATOR_ID))
                .execute()
        }
}

data class TransferData(val amount: Long? = null, val destination: String? = null)
