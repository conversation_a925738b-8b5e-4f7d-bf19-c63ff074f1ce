package hero.stripe.charge.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.gcloud.PubSub
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.model.topics.StripeChargeReceived
import hero.stripe.charge.ChargeCommandService
import hero.stripe.charge.ProcessCharge
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class ChargeWebhookController(
    private val pubSub: PubSub,
    private val chargeCommandService: ChargeCommandService,
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
) {
    @Suppress("unused")
    val routeStripeWebhookCharges: ContractRoute =
        "/v1/webhooks/charges".post(
            summary = "Handle Stripe charge events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.charges)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for charges: ${body.toJson()}")
                val payload = body.eventData?.payload ?: throw BadRequestException("Stripe event payload was null.")
                if (payload.objectType != "charge") {
                    throw BadRequestException("Stripe event was not for `charge`: ${payload.objectType}")
                }
                val chargeId = payload.objectId ?: throw BadRequestException("Field objectId was not given.")

                chargeCommandService.execute(ProcessCharge(chargeId, currency))
                pubSub.publish(StripeChargeReceived.WithId(chargeId, currency))

                Response(Status.NO_CONTENT)
            },
        )
}
