package hero.stripe.subscription.script

import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.SUBSCRIPTION
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import java.io.File

fun main() {
    val pathFile = "/Users/<USER>/Downloads/subscriptions_with_discount_final.csv"
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)

    fillCouponIdsToSubscriptions(File(pathFile).readLines(), context)
}

private fun fillCouponIdsToSubscriptions(
    csv: List<String>,
    context: DSLContext,
) {
    val headers = csv.first().split(",")
    val subscriptionIdIndex = headers.indexOfFirst { it == "id_subscription" }
    val couponIdIndex = headers.indexOfFirst { it == "coupon.id" }

    if (couponIdIndex == -1 && subscriptionIdIndex == -1) {
        error("Coupon id or subscription id columns are missing")
    }

    runBlocking(Dispatchers.Default) {
        csv
            .drop(1)
            .map { it.split(",") }
            .map { line ->
                val subscriptionId = line[subscriptionIdIndex]
                val couponId = line[couponIdIndex]

                subscriptionId to couponId
            }
            .map { (subscriptionId, couponId) ->
                async {
                    assert("sub_" in subscriptionId)
                    assert(couponId.length == 10)
                    assert(couponId.all { it.isUpperCase() })
                    context
                        .update(SUBSCRIPTION)
                        .set(SUBSCRIPTION.COUPON_ID, couponId)
                        .where(SUBSCRIPTION.STRIPE_ID.eq(subscriptionId))
                        .execute()
                    println("Migrated subscription id: $subscriptionId, coupon id: $couponId")
                }
            }
            .awaitAll()
    }
}
