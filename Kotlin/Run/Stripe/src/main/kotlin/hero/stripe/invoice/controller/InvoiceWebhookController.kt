package hero.stripe.invoice.controller

import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.invoice.service.InvoiceCommandService
import hero.stripe.invoice.service.ProcessInvoice
import hero.stripe.model.StripeEventDataObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeWebhookSecrets
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status

class InvoiceWebhookController(
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
    private val invoiceCommandService: InvoiceCommandService,
) {
    @Suppress("unused")
    val routeStripeWebhookPayouts: ContractRoute =
        "/v1/webhooks/invoices".post(
            summary = "Handle Stripe invoice events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.invoices)
                val body = lens<StripeEventRequest<StripeEventDataObject>>(request)
                log.info("Stripe webhooks us for invoices: ${body.toJson()}")
                val payload = body.eventData?.payload ?: throw BadRequestException("Stripe event invoice was null.")
                if (payload.objectType != "invoice") {
                    throw BadRequestException("Stripe event was not for `invoice`: ${payload.objectType}")
                }
                val invoiceId = payload.objectId ?: throw BadRequestException("Field objectId was not given.")

                invoiceCommandService.execute(
                    ProcessInvoice(
                        invoiceId = invoiceId,
                        currency = currency,
                    ),
                )

                Response(Status.NO_CONTENT)
            },
        )
}
