package hero.stripe.invoice.service

import hero.jackson.toJson
import hero.model.Currency
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.StripeInvoiceRecord
import hero.stripe.service.StripeService
import org.jooq.DSLContext
import org.jooq.JSONB
import java.time.Instant

class InvoiceCommandService(
    lazyContext: Lazy<DSLContext>,
    private val stripeService: StripeService,
) {
    private val context by lazyContext

    fun execute(command: ProcessInvoice) {
        val invoice = stripeService.invoice(command.invoiceId, command.currency, listOf("payment_intent"))
        val invoiceRecord = StripeInvoiceRecord().apply {
            this.id = invoice.id
            this.accountName = invoice.accountName
            this.accountCountry = invoice.accountCountry
            this.collectionMethod = invoice.collectionMethod
            this.currency = invoice.currency
            this.customerId = invoice.customer
            this.subscriptionId = invoice.subscription
            this.description = invoice.description
            this.linesData = JSONB.valueOf(invoice.lines.data.toJson())
            this.metadata = JSONB.valueOf((invoice.metadata ?: emptyMap()).toJson())
            this.periodStart = Instant.ofEpochSecond(invoice.periodStart)
            this.periodEnd = Instant.ofEpochSecond(invoice.periodEnd)
            this.status = invoice.status
            this.number = invoice.number
            this.onBehalfOf = invoice.onBehalfOf
            this.billingReason = invoice.billingReason
            this.discounts = arrayOf(invoice.discount?.coupon?.id).filterNotNull().toTypedArray()
            this.createdAt = Instant.ofEpochSecond(invoice.created)
            this.total = invoice.total.toInt()
            this.paymentIntentId = invoice.paymentIntent
            this.transferData = invoice.transferData?.toJson()?.let { JSONB.valueOf(it) }
        }

        context
            .insertInto(Tables.STRIPE_INVOICE)
            .set(invoiceRecord)
            .onDuplicateKeyUpdate()
            .set(invoiceRecord)
            .execute()
    }
}

data class ProcessInvoice(val invoiceId: String, val currency: Currency)
