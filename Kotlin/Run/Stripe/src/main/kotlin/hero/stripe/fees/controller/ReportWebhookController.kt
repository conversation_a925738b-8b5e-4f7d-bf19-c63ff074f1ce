package hero.stripe.fees.controller

import com.stripe.param.reporting.ReportRunCreateParams
import hero.baseutils.atStartOfDay
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.http4k.extensions.example
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.jackson.toJson
import hero.model.Currency
import hero.stripe.common.currencyQuery
import hero.stripe.common.validatePayload
import hero.stripe.model.StripeEventReportObject
import hero.stripe.model.StripeEventRequest
import hero.stripe.model.StripeFeeCsvReportGenerated
import hero.stripe.model.StripeWebhookSecrets
import hero.stripe.service.StripeClients
import org.http4k.contract.ContractRoute
import org.http4k.core.Response
import org.http4k.core.Status
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset

class ReportWebhookController(
    private val webhookSecrets: Map<Currency, StripeWebhookSecrets>,
    private val stripeClients: StripeClients,
    private val pubSub: PubSub,
) {
    private val reportName: String = "card_payments_fees.transaction_level.1"

    @Suppress("unused")
    val routeStripeWebhookReports: ContractRoute =
        "/v1/webhooks/reports".post(
            summary = "Handle Stripe report events.",
            tag = "Stripe webhooks",
            parameters = object {},
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            hideFromOpenApi = true,
            handler = { request, _ ->
                val currency = currencyQuery(request)
                validatePayload(request, webhookSecrets[currency]!!.reports)
                val body = lens<StripeEventRequest<StripeEventReportObject>>(request)
                log.info("Stripe webhooks us for reports: ${body.toJson()}")
                val payload = body.eventData?.payload
                    ?: return@post Response(Status.BAD_REQUEST)

                when (payload.objectType) {
                    // new fees data are available, initiate report run
                    "reporting.report_type" -> {
                        if (payload.objectId == reportName) {
                            val end = payload.end?.let { Instant.ofEpochSecond(it) }
                                ?: error("Field `data_available_end` was null.")
                            // Stripe sends callback for total available period instead of the
                            // new available date. So we process just the last available day.
                            val start = end.atZone(ZoneOffset.UTC).atStartOfDay().minusDays(1).toInstant()

                            // we need to split the stripe-reported interval in small pieces
                            // to prevent the cloud function to timeout on processing long CSVs
                            generateSequencesInInterval(start, end, 5)
                                .forEach {
                                    val params = ReportRunCreateParams.builder()
                                        .setParameters(
                                            ReportRunCreateParams.Parameters.builder()
                                                .setIntervalStart(it.first.epochSecond)
                                                .setIntervalEnd(it.second.epochSecond)
                                                .addColumn("charge_id")
                                                .addColumn("billing_currency")
                                                .addColumn("total_amount")
                                                .addColumn("fee_category")
                                                .addColumn("fee_name")
                                                .addColumn("plan_name")
                                                .addColumn("event_type")
                                                .addColumn("card_country")
                                                .build(),
                                        )
                                        .setReportType(reportName)
                                        .build()

                                    val report = stripeClients[Currency.EUR].reporting().reportRuns().create(params)
                                    log.info("Requested Fees report from ${it.first} as ${report.id}.")
                                }
                        }
                    }
                    // report run succeeded, we request storing the fees
                    // https://files.stripe.com/v1/files/file_1RP0SKB6ZCHekl2RtX7YPUBO/contents
                    "reporting.report_run" -> {
                        pubSub.publish(
                            StripeFeeCsvReportGenerated(
                                currency = Currency.EUR,
                                url = payload.result?.get("url") as? String
                                    ?: error("Webhook `result` field was null."),
                            ),
                        )
                    }
                    else -> log.fatal("Unknown report event: ${body.toJson()}")
                }

                Response(Status.NO_CONTENT)
            },
        )

    internal fun generateSequencesInInterval(
        start: Instant,
        end: Instant,
        minutes: Long,
    ): List<Pair<Instant, Instant>> {
        val step = Duration.ofMinutes(minutes)
        return generateSequence(start) { it + step }
            .takeWhile { it < end }
            .map { it to (it + step).coerceAtMost(end) }
            .toList()
    }
}
