package hero.stripe.payout.service

import hero.baseutils.SystemEnv
import hero.baseutils.instantOf
import hero.baseutils.systemEnv
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Currency
import hero.model.Invoice
import hero.model.InvoiceItemType
import hero.model.Tier
import hero.model.User
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeService
import hero.stripe.service.VatMappingProvider
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.RoundingMode
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class PayoutCommandServiceIT {
    // we test Invoice generation against production Stripe account
    private val production = true
    private val firestore = firestore(SystemEnv.cloudProject, production)
    // we need production key to be able to test against production invoices
    private val stripeClients = StripeClients(systemEnv("STRIPE_API_KEY_EU_PROD"), SystemEnv.stripeKeyUs)
    private val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()
    private val stripe: StripeService = StripeService(clients = stripeClients, pubSub = null)

    private val underTest = spyk(
        PayoutCommandService(
            invoicesCollection = mockk(),
            hostnameServices = "https://svc-devel.herohero.co",
            pubSub = mockk(),
            stripe = stripe,
            usersCollection = firestore.typedCollectionOf(User),
            tiersCollection = firestore.typedCollectionOf(Tier),
            countryToVatMapping = countryToVatMapping,
        ),
    )

    @BeforeEach
    fun beforeEach() {
        every { underTest.storeInvoice(any(), any(), any(), any()) } just runs
        every { underTest.nextInvoiceId(any(), any(), any()) } returns "202020-020202"
        every { underTest.getInvoiceForPayout(any()) } returns null
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `slovakian invoice of VAT payer has zero VAT with 10 percent fee`() {
        val accountId = "acct_1NQnUoBQCOC15urV"
        val payoutId = "po_1NZkX0BQCOC15urVuDVTnxlF"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.EUR, invoice.currencyPayout)
        assertEquals(2662_20, invoice.total)
        assertEquals(4, invoice.items.size)
        assertEquals(75 + 184, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(2958_00, invoice.totalTurnover)
        assertEquals(75 + 184, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(295_80, invoice.totalFee)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        // SK invoices for VAT payers (reverse charged in target country) can never have VAT
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(0, it.vatCents) }
        assertTrue(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("GoUp!", invoice.invoicedCompany.namePublic)
        assertEquals("SEDLO, s.r.o.", invoice.invoicedCompany.name)
    }

    @Test
    fun `slovakian invoice of non-VAT payer has non-zero VAT with 10 percent fee`() {
        val accountId = "acct_1NSbDGPjAuTO1BME"
        val payoutId = "po_1NZkZ6PjAuTO1BMEP9pF45rA"
        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        val skVat = countryToVatMapping["SK", invoice!!.timestamp]
        assertNotNull(invoice)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.EUR, invoice.currencyPayout)
        assertEquals(4_50, invoice.total)
        assertEquals(4_5000, invoice.total4D)
        assertEquals(2, invoice.items.size)
        assertEquals(1, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(5_00, invoice.totalTurnover)
        assertEquals(5_0000, invoice.totalTurnover4D)
        assertEquals(1, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(50, invoice.totalFee)
        assertEquals(5000, invoice.totalFee4D)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        // SK invoices for non-VAT payers (non-reverse charged) will have VAT of their country
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(skVat, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertTrue(invoice.euOneStopShop)
        assertEquals("renkasekerakova", invoice.invoicedCompany.namePublic)
        assertEquals("Mgr. Renáta Ďordevič", invoice.invoicedCompany.name)
    }

    @Test
    fun `czech invoice created when herohero was non-vat payer has zero VAT with 10 percent fee`() {
        val accountId = "acct_1MLU9kBFNkJz7c1S"
        val payoutId = "po_1MrWM5BFNkJz7c1SbwYMOE1q"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.CZK, invoice.currencyPayout)
        assertEquals(24_30, invoice.total)
        assertEquals(24_3000, invoice.total4D)
        assertEquals(4, invoice.items.size)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(27_00, invoice.totalTurnover)
        assertEquals(27_0000, invoice.totalTurnover4D)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(2_70, invoice.totalFee)
        assertEquals(2_7000, invoice.totalFee4D)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        // CZ invoice before 2023/04 never had VAT
        assertTrue(invoice.items.filter { it.type == InvoiceItemType.FEE }.all { it.vatCents == 0 })
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Napíšu ti dopis♡", invoice.invoicedCompany.namePublic)
        // company not yet filled in
        assertEquals(null, invoice.invoicedCompany.name)
    }

    @Test
    fun `czech invoice created when herohero was vat payer has non-zero VAT with 10 percent fee`() {
        val accountId = "acct_1MLU9kBFNkJz7c1S"
        val payoutId = "po_1N1ffoBFNkJz7c1S49aJOF9E"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.CZK, invoice.currencyPayout)
        assertEquals(24_30, invoice.total)
        assertEquals(24_3000, invoice.total4D)
        assertEquals(4, invoice.items.size)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(27_00, invoice.totalTurnover)
        assertEquals(27_0000, invoice.totalTurnover4D)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(2_70, invoice.totalFee)
        assertEquals(2_7000, invoice.totalFee4D)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        // CZ invoice after 2023/04 has VAT
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(21, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Napíšu ti dopis♡", invoice.invoicedCompany.namePublic)
        assertEquals(null, invoice.invoicedCompany.name)
    }

    @Test
    fun `czech invoice with legacy fees is correctly processed`() {
        val accountId = "acct_1I73HWPbUHpD9pR5"
        val payoutId = "po_1IasdjPbUHpD9pR5opw2vYHh"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2021-03-31T01:14:35Z"), invoice.timestamp)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.CZK, invoice.currencyPayout)
        assertEquals(232_20, invoice.total)
        assertEquals(232_2000, invoice.total4D)
        assertEquals(2, invoice.items.size)
        assertEquals(43, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(258_00, invoice.totalTurnover)
        assertEquals(258_0000, invoice.totalTurnover4D)
        assertEquals(43, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(25_80, invoice.totalFee)
        assertEquals(25_8000, invoice.totalFee4D)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(0, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Vyhonit ďábla", invoice.invoicedCompany.namePublic)
        assertEquals("Terézia Ferjančeková", invoice.invoicedCompany.name)
    }

    @Test
    fun `invoice with manual adjustment is correctly generated`() {
        val accountId = "acct_1Lw8ZXPcJonOqsUM"
        val payoutId = "po_1MW8NCPcJonOqsUM6gNT7jIU"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2023-01-31T01:10:58Z"), invoice.timestamp)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.EUR, invoice.currencyPayout)
        assertEquals(591_40, invoice.total)
        assertEquals(4, invoice.items.size)
        assertEquals(110, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(657_11, invoice.totalTurnover)
        assertEquals(110, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(65_71, invoice.totalFee)
        // until 2023-09-01 the fee was 10 %
        assertFee(10_00, invoice)
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(0, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("The City Survival", invoice.invoicedCompany.namePublic)
        assertEquals("The City Survival", invoice.invoicedCompany.name)
    }

    private fun assertFee(
        fee: Int,
        invoice: Invoice,
    ) {
        assertEquals(
            fee,
            invoice.totalFee4D.toBigDecimal()
                .divide(invoice.totalTurnover4D.toBigDecimal(), 4, RoundingMode.HALF_UP)
                // we drop the 4th decimal because of rounding errors
                .setScale(3, RoundingMode.HALF_UP)
                .movePointRight(4)
                .toInt(),
        )
    }

    @Test
    fun `US invoice is correctly generated`() {
        val accountId = "acct_1NpuEBBGEicL3v1h"
        val payoutId = "po_1O76fBBGEicL3v1hwPjrH3OG"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2023-10-31T01:22:37Z"), invoice.timestamp)
        assertEquals(Currency.USD, invoice.currencyInvoice)
        assertEquals(Currency.USD, invoice.currencyPayout)
        assertEquals(202_50, invoice.total)
        assertEquals(2, invoice.items.size)
        assertEquals(45, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(225_00, invoice.totalTurnover)
        assertEquals(45, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(22_50, invoice.totalFee)
        // US fee is still 10 %
        assertFee(10_00, invoice)
        // US invoices has no vat
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(0, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Bethany Watson & Dennis Cahlo", invoice.invoicedCompany.namePublic)
        assertEquals("Cary Allen Productions LLC", invoice.invoicedCompany.name)
    }

    @Test
    fun `invoice is correctly generated with 12_1 percent fee`() {
        val accountId = "acct_1MLU9kBFNkJz7c1S"
        val payoutId = "po_1NvV6jBFNkJz7c1SI4JWkrMQ"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2023-09-29T01:03:05Z"), invoice.timestamp)
        assertEquals(Currency.EUR, invoice.currencyInvoice)
        assertEquals(Currency.CZK, invoice.currencyPayout)
        assertEquals(24_61, invoice.total)
        assertEquals(24_6100, invoice.total4D)
        assertEquals(4, invoice.items.size)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(28_00, invoice.totalTurnover)
        assertEquals(28_0000, invoice.totalTurnover4D)
        assertEquals(5, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(3_39, invoice.totalFee)
        assertEquals(3_3900, invoice.totalFee4D)
        // 2023-09-01 the fee was 12.1 %
        // unfortunately for 5 €, the fee is transfer is 5.00 * 0.8789 = 4.3945 =~ 4.40,
        assertFee(12_10, invoice)
        // CZ invoice after 2023/04 has VAT
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(21, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Napíšu ti dopis♡", invoice.invoicedCompany.namePublic)
        assertEquals(null, invoice.invoicedCompany.name)
    }

    @Test
    fun `US invoice with Stripe fees separated is generated`() {
        val accountId = "acct_1RJJXpBOSXndGBvF"
        val payoutId = "po_1RNjpJBOSXndGBvFhok77kor"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2025-05-12T00:02:37Z"), invoice.timestamp)
        assertEquals(Currency.USD, invoice.currencyInvoice)
        assertEquals(Currency.USD, invoice.currencyPayout)
        assertEquals(44_70, invoice.total)
        assertEquals(44_7000, invoice.total4D)
        assertEquals(6, invoice.items.size)
        assertEquals(10, invoice.items.filter { it.type == InvoiceItemType.TURNOVER }.sumOf { it.count })
        assertEquals(50_00, invoice.totalTurnover)
        assertEquals(50_0000, invoice.totalTurnover4D)
        assertEquals(18, invoice.items.filter { it.type == InvoiceItemType.FEE }.sumOf { it.count })
        assertEquals(5_30, invoice.totalFee)
        assertEquals(5_3000, invoice.totalFee4D)
        // this is an average over 4 transactions with structured fees and 6 trx. pure 7 % ones
        assertFee(10_60, invoice)
        invoice.items.filter { it.type == InvoiceItemType.FEE }.forEach { assertEquals(0, it.vatCents) }
        assertFalse(invoice.euReverseCharged)
        assertFalse(invoice.euOneStopShop)
        assertEquals("Poppies Studios Plus", invoice.invoicedCompany.namePublic)
        assertEquals("Poppies Studios", invoice.invoicedCompany.name)
    }

    @Test
    fun `US invoice with Stripe with fees refund is generated`() {
        val accountId = "acct_1NzhZtBLSS9dOseb"
        val payoutId = "po_1RUzLpBLSS9dOsebADioXFwr"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        assertNotNull(invoice)
        assertEquals(instantOf("2025-05-31T00:02:09Z"), invoice.timestamp)
        assertEquals(Currency.USD, invoice.currencyInvoice)
        assertEquals(Currency.USD, invoice.currencyPayout)
        assertEquals(944_8500, invoice.total4D)
        assertEquals(105_1500, invoice.totalFee4D)
    }

    @Test
    fun `late payout is still shifted into previous month`() {
        val accountId = "acct_1PriLXBDDn66fvKg"
        val payoutId = "po_1RJkikBDDn66fvKgO6D9ihxC"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )

        assertNotNull(invoice)
        assertEquals(instantOf("2025-04-30T00:11:22Z"), invoice.timestamp)
    }

    @Test
    fun `payout reversal is handled`() {
        val accountId = "acct_1Lw061BRF6jLq85r"
        val payoutId = "po_1OFU2pBRF6jLq85rexo0RNZa"

        val invoice = underTest.execute(
            ProcessPayout(
                accountId,
                payoutId,
                Currency.EUR,
                overwriteInvoice = false,
                sendEmail = false,
            ),
        )
        // this payout contains no actual fields to be invoiced
        assertNull(invoice)
    }

    @Test
    fun `payout with manual payout transfer must fail to be processed automatically`() {
        val accountId = "acct_1NCn3hBLDXC7PgRo"
        val payoutId = "po_1OsyDrBLDXC7PgRoUVZ0HZpV"

        assertThrows<IllegalStateException> {
            underTest.execute(
                ProcessPayout(
                    accountId,
                    payoutId,
                    Currency.EUR,
                    overwriteInvoice = false,
                    sendEmail = false,
                ),
            )
        }
    }
}
