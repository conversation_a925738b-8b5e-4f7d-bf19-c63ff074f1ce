package hero.stripe.subscription.service

import com.stripe.exception.StripeException
import com.stripe.model.Coupon
import hero.baseutils.SystemEnv
import hero.baseutils.mockNow
import hero.model.Currency
import hero.stripe.service.StripeClients
import io.mockk.clearAllMocks
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertNotNull
import org.junit.jupiter.api.assertNull

class SubscriptionCommandServiceIT {
    @BeforeEach
    fun before() {
        mockNow("2020-01-01T00:00:00Z")
    }

    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
    )

    private val service = spyk(
        SubscriptionCommandService(
            stripeService = mockk(),
            usersCollection = mockk(),
            couponCommandService = mockk(),
            pubSub = mockk(),
            subscriptionService = mockk(),
            stripeClients = stripeClients,
            stripeSubscriberSaver = mockk(),
            lazyContext = mockk(),
        ),
    )

    @AfterEach
    fun after() {
        clearAllMocks()
        unmockkAll()
        try {
            stripeClients[Currency.EUR].coupons().delete("RECREATE_TEST")
        } catch (e: StripeException) {
            // nevermind
        }
    }

    @Test
    fun `should not recreate coupon for 100 procent discounts`() {
        val coupon = Coupon().also {
            it.id = "test"
            it.maxRedemptions = 1
            it.percentOff = 100.toBigDecimal()
        }
        assertNull(service.restoreCoupon(coupon, Currency.EUR, ""))
    }

    @Test
    fun `should not recreate coupon multi-redemption discounts`() {
        val coupon = Coupon().also {
            it.id = "test"
            it.maxRedemptions = 10
            it.percentOff = 70.toBigDecimal()
        }
        assertNull(service.restoreCoupon(coupon, Currency.EUR, ""))
    }

    @Test
    fun `should recreate single redemption discounts`() {
        val coupon = Coupon().also {
            it.id = "RECREATE_TEST"
            it.maxRedemptions = 1
            it.percentOff = 70.toBigDecimal()
            it.duration = "repeating"
            it.durationInMonths = 10
            it.metadata = mapOf("recreatedCount" to "2", "metadata" to "test")
        }
        val newCoupon = service.restoreCoupon(coupon, Currency.EUR, "")

        assertNotNull(newCoupon)
        assertEquals(coupon.id, newCoupon.id)
        assertEquals(1, newCoupon.maxRedemptions)
        assertEquals(70, newCoupon.percentOff.toInt())
        assertEquals(
            mapOf("metadata" to "test", "recreatedCount" to "3", "recreatedAt" to "2020-01-01T00:00:00Z"),
            newCoupon.metadata,
        )
    }
}
