package hero.stripe.payout.service

import hero.baseutils.instantOf
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.get
import hero.model.Invoice
import hero.stripe.service.VatMapping
import hero.test.IntegrationTestHelper
import hero.test.gcloud.FirestoreTestDatabase
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals

class InvoiceIdUniquenessIT {
    private val helper = IntegrationTestHelper()
    private val firestore = FirestoreRef(FirestoreTestDatabase.testFirestore, false)
    private val invoicesCollection = firestore.firestore["test-invoices"]
    private val invoiceRepository = TypedCollectionReference<Invoice>(invoicesCollection)

    private val underTest = PayoutCommandService(
        invoicesCollection = invoiceRepository,
        hostnameServices = "https://svc-devel.herohero.co",
        pubSub = mockk(),
        stripe = mockk(),
        usersCollection = mockk(),
        tiersCollection = mockk(),
        countryToVatMapping = VatMapping(mapOf()),
    )

    @Test
    fun `two invoices will not get same invoiceId`() {
        val now = instantOf("2023-01-01T12:00:00Z")
        val payoutIdOne = "po_1111122222"
        val payoutIdTwo = "po_2222233333"

        // first invoice has a unique invoiceId
        val invoiceIdOne = underTest.nextInvoiceId(now, payoutIdOne, seed = 0)
        assertEquals("202301-490450", invoiceIdOne)
        val invoiceOne = helper.createInvoice(timestamp = now, payoutId = payoutIdOne, invoiceId = invoiceIdOne)
        invoiceRepository[invoiceOne.id].set(invoiceOne)

        // trying to generate an invoiceId with the same payoutId will return existing invoiceId
        val invoiceIdOneDuplicate = underTest.nextInvoiceId(now, payoutIdOne, seed = 0)
        assertEquals(invoiceOne.invoiceId, invoiceIdOneDuplicate)

        // but different payout will certainly receive a new invoiceId
        val invoiceIdTwo = underTest.nextInvoiceId(now, payoutIdTwo, seed = 0)
        assertNotEquals(invoiceIdOne, invoiceIdTwo)
        assertEquals("202301-686605", invoiceIdTwo)
    }
}
