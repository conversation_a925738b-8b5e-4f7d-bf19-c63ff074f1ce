package hero.api.library.service

import hero.api.library.service.dto.SavedPostWithData
import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class LibraryQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetSavedPosts {
        @Test
        fun `should return saved posts for a user in descending order by savedAt`() {
            val underTest = LibraryQueryService(lazyTestContext, TestCollections.savedPostsCollection)

            val now = Instant.now()
            testHelper.createUser("alex")

            val post1 = testHelper.createPost("cestmir")
            val savedPost1 = testHelper.createSavedPost("alex", post1, savedAt = now - 5.seconds)

            val post2 = testHelper.createPost("cestmir")
            val savedPost2 = testHelper.createSavedPost("alex", post2, savedAt = now)

            val savedPosts = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(), PageRequest()))
            assertThat(savedPosts.content)
                .containsExactly(
                    SavedPostWithData(savedPost2, post2),
                    SavedPostWithData(savedPost1, post1),
                )
        }

        @Test
        fun `should paginate using after cursor order by savedAt descending`() {
            val underTest = LibraryQueryService(lazyTestContext, TestCollections.savedPostsCollection)

            val now = Instant.now()
            testHelper.createUser("alex")

            val post1 = testHelper.createPost("cestmir")
            val savedPost1 = testHelper.createSavedPost("alex", post1, savedAt = now - 10.seconds)

            val post2 = testHelper.createPost("cestmir")
            val savedPost2 = testHelper.createSavedPost("alex", post2, savedAt = now - 5.seconds)

            val post3 = testHelper.createPost("cestmir")
            val savedPost3 = testHelper.createSavedPost("alex", post3, savedAt = now)

            val firstPage = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(), PageRequest(pageSize = 1)))
            assertThat(firstPage.content).containsExactly(SavedPostWithData(savedPost3, post3))
            assertThat(firstPage.hasNext).isTrue()

            val secondPage = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(), firstPage.nextPageable))
            assertThat(secondPage.content).containsExactly(SavedPostWithData(savedPost2, post2))
            assertThat(secondPage.hasNext).isTrue()

            val thirdPage = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(), secondPage.nextPageable))
            assertThat(thirdPage.content).containsExactly(SavedPostWithData(savedPost1, post1))
            assertThat(thirdPage.hasNext).isFalse()
        }

        @Test
        fun `should return only return saved posts from users library and not other user's saved posts`() {
            val underTest = LibraryQueryService(lazyTestContext, TestCollections.savedPostsCollection)
            testHelper.createUser("alex")
            testHelper.createUser("not-alex")

            val post1 = testHelper.createPost("cestmir")
            val savedPost1 = testHelper.createSavedPost("alex", post1)

            val post2 = testHelper.createPost("cestmir")
            testHelper.createSavedPost("not-alex", post2)

            val savedPosts = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(), PageRequest()))
            assertThat(savedPosts.content).containsExactly(SavedPostWithData(savedPost1, post1))
        }

        @Test
        fun `should return only posts from creator's user currently subscribes`() {
            val underTest = LibraryQueryService(lazyTestContext, TestCollections.savedPostsCollection)
            testHelper.createUser("alex")

            val now = Instant.now()

            val post1 = testHelper.createPost("cestmir")
            val savedPostActiveSub = testHelper.createSavedPost("alex", post1, savedAt = now - 5.seconds, active = true)

            val post2 = testHelper.createPost("cestmir")
            testHelper.createSavedPost("alex", post2, savedAt = now, active = false)

            val savedPosts = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(true), PageRequest()))
            assertThat(savedPosts.content)
                .containsExactly(SavedPostWithData(savedPostActiveSub, post1))
        }

        @Test
        fun `should return all posts library, even from authors that user no longer subscribes`() {
            val underTest = LibraryQueryService(lazyTestContext, TestCollections.savedPostsCollection)
            testHelper.createUser("alex")

            val now = Instant.now()

            val post1 = testHelper.createPost("cestmir")
            val savedPost1 = testHelper.createSavedPost("alex", post1, savedAt = now - 5.seconds, active = true)

            val post2 = testHelper.createPost("themag")
            val savedPost2 = testHelper.createSavedPost("alex", post2, savedAt = now, active = false)

            val savedPosts = underTest.execute(GetSavedPosts("alex", GetSavedPostsFilter(false), PageRequest()))
            assertThat(savedPosts.content)
                .containsExactly(
                    SavedPostWithData(savedPost2, post2),
                    SavedPostWithData(savedPost1, post1),
                )
        }
    }
}
