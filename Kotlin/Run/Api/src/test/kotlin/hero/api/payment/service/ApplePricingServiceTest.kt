package hero.api.payment.service

import hero.model.Currency
import hero.model.Tier
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class ApplePricingServiceTest {
    private val underTest = ApplePricingService()

    @Test
    fun `compute EUR price`() {
        assertEquals(
            799,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR05")),
        )

        assertEquals(
            999,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR06")),
        )

        assertEquals(
            1599,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR10")),
        )
    }

    @Test
    fun `compute USD price`() {
        assertEquals(
            799,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR05")),
        )

        assertEquals(
            999,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR06")),
        )

        assertEquals(
            1599,
            underTest.computeApplePriceCents(Currency.EUR, Tier.ofId("EUR10")),
        )
    }

    @Test
    fun `compute CZK price`() {
        assertEquals(
            19900,
            underTest.computeApplePriceCents(Currency.CZK, Tier.ofId("EUR05")),
        )

        assertEquals(
            23900,
            underTest.computeApplePriceCents(Currency.CZK, Tier.ofId("EUR06")),
        )

        assertEquals(
            39900,
            underTest.computeApplePriceCents(Currency.CZK, Tier.ofId("EUR10")),
        )
    }

    @Test
    fun `unknown price is refused`() {
        assertThrows<IllegalStateException> {
            underTest.computeApplePriceCents(Currency.GBP, Tier.ofId("EUR05"))
        }
    }
}
