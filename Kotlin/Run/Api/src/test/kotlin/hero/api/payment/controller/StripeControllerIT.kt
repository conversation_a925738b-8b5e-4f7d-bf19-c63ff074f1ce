package hero.api.payment.controller

import hero.api.subscriber.repository.SubscribersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.SystemEnv
import hero.gcloud.FirestoreRef
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.jackson.fromJson
import hero.model.Creator
import hero.model.Currency.EUR
import hero.model.Role
import hero.model.User
import hero.stripe.model.StripeConnectResponse
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripeClients
import hero.stripe.service.StripePaymentMethodsService
import hero.stripe.service.StripeService
import hero.stripe.service.VatMapping
import hero.test.IntegrationTestHelper
import hero.test.StripeHelper
import hero.test.gcloud.FirestoreTestDatabase
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class StripeControllerIT {
    private val isProduction = false
    private val testHelper = IntegrationTestHelper()
    private val firestore = FirestoreRef(FirestoreTestDatabase.testFirestore, isProduction)
    private val stripeClients = StripeClients(
        keysEu = SystemEnv.stripeKeyEu,
        keysUs = SystemEnv.stripeKeyUs,
        production = isProduction,
    )
    private val stripeHelper = StripeHelper(stripeClients[EUR])
    private val stripeService = spyk(StripeService(stripeClients, null))
    private val stripePaymentMethods = spyk(StripePaymentMethodsService(stripeClients, stripeService, null))

    private val usersCollection = firestore.typedCollectionOf(User)

    val stripeAccountService = StripeAccountService(
        clients = stripeClients,
        hostname = "https://local.herohero.co",
        hostnameServices = "https://svc-local.herohero.co",
    )

    private val userRepository: UsersRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = mockk(),
            pubSub = mockk(),
            subscriberCollection = mockk(),
            collection = usersCollection,
            userIdGenerator = mockk(),
            accountService = stripeAccountService,
            userRepository = mockk(),
        ),
    )

    private val subscriberRepository: SubscribersRepository = mockk()

    private val controller = StripeController(
        production = false,
        hostname = "https://local.herohero.co",
        stripe = stripeService,
        subscriberRepository = subscriberRepository,
        subscriberStripeRepository = mockk(),
        tierRepository = mockk(),
        userRepository = userRepository,
        categoriesRepository = mockk(),
        stripePublicKeys = mapOf(),
        stripePaymentsService = mockk(),
        stripePaymentMethods = stripePaymentMethods,
        stripeAccountService = stripeAccountService,
        subscriptionService = mockk(),
        countryToVatMapping = VatMapping(mapOf()),
        cancelSubscriptionCommandService = mockk(),
        subscribersCollection = mockk(),
        invoicesCollection = mockk(),
    )

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
    }

    @AfterAll
    fun afterAll() {
        stripeHelper.testCleanUp()
    }

    @Test
    fun `create stripe connection-link for user`() {
        val user = testHelper.createUser()
        every { userRepository.get(any(), user.id) } returns user
        val request = Request(Method.POST, "/v1/stripe/connection-links")
            .withAccessTokenCookie(jwtFor(user.id))
        val accountId = slot<String>()
        every { userRepository.get(request) } returns user
        mockkStatic("hero.gcloud.FirestoreKt")
        every {
            userRepository.collection[user.id]
                .field(root(User::creator).path(Creator::stripeAccountId))
                .update(capture(accountId))
        } just runs
        val response1 = controller.routeStripeConnect(request)
        assertNotNull(accountId.captured)
        val payload1 = response1.bodyString().fromJson<StripeConnectResponse>()
        assertEquals(user.creator.stripeAccountId, accountId.captured)
        assertNotNull(payload1.url)
        assertTrue(Instant.now() < payload1.expires)

        // try again - the accountId must not change
        val response2 = controller.routeStripeConnect(request)
        val firstAccountId = user.creator.stripeAccountId
        val payload2 = response2.bodyString().fromJson<StripeConnectResponse>()
        assertEquals(firstAccountId, accountId.captured)
        assertEquals(user.creator.stripeAccountId, accountId.captured)
        assertNotNull(payload2.url)
        assertNotEquals(payload1.url, payload2.url)
        // there are no millis, these might even be equal
        assertTrue(payload1.expires <= payload2.expires)
        assertTrue(Instant.now() < payload2.expires)
    }

    @Test
    fun `stripe account id is correctly deleted on request`() {
        val admin = testHelper.createUser(role = Role.MODERATOR)
        val creator = testHelper.createUser(stripeAccountId = "mock_account_id")

        val request = Request(Method.DELETE, "/v1/users/${creator.id}/stripe-account")
            .withAccessTokenCookie(jwtFor(admin.id))

        usersCollection[creator.id].set(creator)
        every { userRepository.get(request) } returns admin
        every { userRepository.get(creator.id) } returns creator
        every { stripeService.deleteAccount(creator.creator.stripeAccountId!!, EUR, any(), admin.id) } just runs

        controller.routeDeleteStripeAccount(request)

        val patchedUser = usersCollection[creator.id].get()
        assertNull(patchedUser.creator.stripeAccountId)
        assertTrue(creator.creator.stripeAccountId in patchedUser.creator.stripeAccountLegacyIds)
        assertFalse(patchedUser.creator.stripeAccountActive)
        assertFalse(patchedUser.creator.stripeAccountOnboarded)
    }
}
