package hero.api.post.service

import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.model.SupportCounts
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class FeaturedPostsQueryServiceIT : IntegrationTest() {
    @Test
    fun `should fetch posts sorted by published from popular creators who have previews on`() {
        val underTest = prepareService()

        val now = Instant.ofEpochSecond(9999)

        // we should be able to see new posts that have preview from this creator
        val creator1 = testHelper.createUser("alex", counts = SupportCounts(supporters = 100), hasPostPreviews = true)

        // we should not see posts from creators that don't have previews
        val creator2 = testHelper.createUser("pavel", counts = SupportCounts(supporters = 100), hasPostPreviews = false)

        // we should not see posts from creators that are not popular
        val creator3 = testHelper.createUser("themag", counts = SupportCounts(supporters = 0), hasPostPreviews = true)

        val post1 = testHelper.createPost(creator1.id, hasPreview = true, publishedAt = now - 5.seconds)
        val post2 = testHelper.createPost(creator1.id, hasPreview = false)
        val post3 = testHelper.createPost(creator1.id, hasPreview = true, publishedAt = now)
        val post4 = testHelper.createPost(creator2.id, hasPreview = true)
        val post5 = testHelper.createPost(creator3.id, hasPreview = true)

        val result = underTest.execute(GetNewPostsFromPopularCreators(10, PageRequest()))
        assertThat(result.content)
            .containsExactly(post3, post1)
            .doesNotContain(post2, post4, post5)
    }

    @Test
    fun `should correctly page`() {
        val underTest = prepareService()

        val now = Instant.ofEpochSecond(9999)

        val creator1 = testHelper.createUser("alex", counts = SupportCounts(supporters = 100), hasPostPreviews = true)

        val post1 = testHelper.createPost(creator1.id, hasPreview = true, publishedAt = now - 5.seconds)
        testHelper.createPost(creator1.id, hasPreview = false)
        val post3 = testHelper.createPost(creator1.id, hasPreview = true, publishedAt = now)

        val result1 = underTest.execute(GetNewPostsFromPopularCreators(10, PageRequest(pageSize = 1)))
        assertThat(result1.content).contains(post3)
        assertThat(result1.hasNext).isTrue

        val result2 = underTest.execute(GetNewPostsFromPopularCreators(10, result1.nextPageable))
        assertThat(result2.content).contains(post1)
        assertThat(result2.hasNext).isFalse
    }

    private fun prepareService(): FeaturedPostsQueryService = FeaturedPostsQueryService(lazyTestContext)
}
