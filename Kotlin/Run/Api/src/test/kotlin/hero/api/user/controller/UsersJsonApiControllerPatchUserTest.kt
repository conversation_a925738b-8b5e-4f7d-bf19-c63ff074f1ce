package hero.api.user.controller

import hero.api.category.repository.CategoriesRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.md5nice
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.jackson.toJson
import hero.model.CompanyType
import hero.model.Creator
import hero.model.Currency
import hero.model.Path
import hero.model.Tier
import hero.model.TierDtoRelationship
import hero.model.User
import hero.model.UserCompany
import hero.model.UserDto
import hero.model.UserDtoAttributes
import hero.model.UserDtoRelationships
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

internal class UsersJsonApiControllerPatchUserTest {
    private val tierRepository: TiersRepository = mockk()

    private val userRepository: UsersRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = mockk(),
            pubSub = mockk(),
            subscriberCollection = mockk(),
            collection = mockk(),
            accountService = mockk(),
            userRepository = mockk(),
        ),
    )

    private val categoriesRepository: CategoriesRepository = mockk()

    private val controller = UsersJsonApiController(
        categoriesRepository = categoriesRepository,
        repository = userRepository,
        pubSub = mockk(),
        tierRepository = tierRepository,
        rssFeedsRepository = mockk(),
        deleteUserCommandService = mockk(),
    )

    private fun testUser(currency: Currency = Currency.EUR): User =
        User(
            // note that id must be [a-z-]+
            id = "test-user-${currency.name.lowercase()}-${System.currentTimeMillis().toString().md5nice()}",
            name = "Andreas Muller",
            bio = "bio",
            bioHtml = "<strong>bio</strong>",
            bioEn = "bio in en",
            bioHtmlEn = "<strong>bio in en</strong>",
            email = "<EMAIL>",
            path = "andmul",
            creator = Creator(tierId = "${currency}05", currency = currency),
            hasRssFeed = false,
            company = UserCompany(),
        )

    private fun testBody(
        name: String = "Andreas Muller",
        bio: String = "bio",
        path: String = "andmul",
        tierId: String = "EUR05",
        vatId: String? = null,
        phone: String? = null,
        birthDate: String? = null,
    ): UserDto =
        UserDto(
            id = "id",
            attributes = UserDtoAttributes(
                name = name,
                bio = bio,
                bioHtml = "<strong>$bio</strong>",
                bioEn = "$bio in en",
                bioHtmlEn = "<strong>$bio in en</strong>",
                path = path,
                image = null,
                language = "cs",
                hasRssFeed = true,
                hasSpotifyExport = true,
                hasLivestreams = false,
                discord = null,
                company = UserCompany(
                    name = name,
                    isIndividual = true,
                    companyType = CompanyType.INDIVIDUAL,
                    vatId = vatId,
                    phone = phone,
                    birthDate = birthDate,
                ),
                lastChargeFailedAt = null,
                gjirafaLivestream = null,
            ),
            relationships = UserDtoRelationships(
                tier = TierDtoRelationship(tierId),
                categories = emptyList(),
            ),
        )

    @BeforeEach
    fun beforeEach() {
        every { userRepository.store(any()) } just runs
        every { categoriesRepository.list(any()) } returns listOf()
    }

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `nothing can be changed by unauthorized user`() {
        val user = testUser()

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie("1234")
            .body(testBody().toJson())

        val exception = assertThrows<UnauthorizedException> {
            controller.routePatchUser(request)
        }
        assertEquals("Unauthorized.", exception.message)
    }

    @Test
    fun `name must be at least 2 characters long with valid alphanumeric chars`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(name = "\u2001\u2002\u2003\u2004\u200E\u200E\u200E").toJson())

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
        assertEquals("Field 'name' of value '\u200E\u200E\u200E' is invalid: min_length_two", exception.message)
    }

    @Test
    fun `name must not be empty`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(name = "").toJson())

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
        assertEquals("Field 'name' of value '' is invalid: min_length_two", exception.message)
    }

    @Test
    fun `too short paths are denied`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val shortPath = "ne"
        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(
                testBody(path = shortPath).toJson(),
            )

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }

        assertEquals("Field 'path' of value '$shortPath' is invalid: min_length_two", exception.message)
    }

    @ParameterizedTest
    @ValueSource(strings = ["ščěč", "AZZ", "a-z", "a_z", "aBc"])
    fun `path containing invalid characters is denied`(invalidPath: String) {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(path = invalidPath).toJson())

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }

        assertEquals("Field 'path' of value '$invalidPath' is invalid: lowercase_alphanumeric", exception.message)
    }

    @Test
    fun `path taken by a different user cannot be set`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val takenPath = "alreadytaken"
        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(
                testBody(
                    name = "name",
                    bio = "bio",
                    path = takenPath,
                    tierId = "EUR05",
                ).toJson(),
            )

        every { userRepository.path(takenPath) } returns Path(userId = "ann-ilv", id = "ann-ilv")

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }

        assertEquals("User.path is already taken by other user: $takenPath", exception.message)
    }

    @ParameterizedTest
    @ValueSource(strings = ["search", "create", "post", "hero", "herohero", "heroheroco", "coherohero"])
    fun `paths containing some predefined strings are denied`(disallowedPath: String) {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(
                UserDto(
                    id = "id",
                    attributes = UserDtoAttributes(
                        name = "name",
                        bio = "bio",
                        path = disallowedPath,
                        image = null,
                        language = "cs",
                        hasRssFeed = true,
                        hasSpotifyExport = true,
                        hasLivestreams = false,
                        discord = null,
                        lastChargeFailedAt = null,
                        gjirafaLivestream = null,
                    ),
                    relationships = UserDtoRelationships(
                        tier = TierDtoRelationship("EUR05"),
                        categories = emptyList(),
                    ),
                ).toJson(),
            )

        every { userRepository.path(disallowedPath) } returns Path(userId = "ann-ilv", id = "")

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }

        assertEquals("Field 'path' of value '$disallowedPath' is invalid: illegal_string", exception.message)
    }

    @Test
    fun `changing path repeatedly is denied`() {
        val user = testUser()
            .also { it.pathChanged = Instant.now() }
        every { userRepository.get(any(), user.id) } returns user

        val regularPath = "regularpath"
        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(path = regularPath).toJson())

        every { userRepository.get(request, user.id) } returns user
        every { userRepository.path(regularPath) } returns null

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }

        assertEquals("Field 'path' of value '$regularPath' is invalid: path_change_too_often", exception.message)
    }

    @Test
    fun `valid path is correctly set`() {
        val correctPath = "newpath"

        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(path = correctPath).toJson())

        every { userRepository.path(correctPath) } returns null
        every { tierRepository.get("EUR05") } returns Tier.ofId("EUR05")

        controller.routePatchUser(request)

        assertEquals(correctPath, user.path)
        assertTrue(Instant.now().minusSeconds(10) < user.pathChanged)
    }

    @Test
    fun `non-existing Tier cannot be set`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val tierId = "CZK01"
        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(tierId = tierId).toJson())

        every { tierRepository.get(tierId) } throws NotFoundException("Tier does not exist.")

        val exception = assertThrows<NotFoundException> {
            controller.routePatchUser(request)
        }

        assertEquals("Tier does not exist.", exception.message)
    }

    @Test
    fun `changing Tier to from EUR to USD is denied`() {
        val user = testUser(Currency.EUR)
        every { userRepository.get(any(), user.id) } returns user

        val tierId = "USD05"
        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(tierId = tierId).toJson())

        every { tierRepository["USD05"] } returns Tier.ofId(tierId)

        val exception = assertThrows<ConflictException> {
            controller.routePatchUser(request)
        }

        assertEquals(
            "Creator ${user.id} has already fixed currency to EUR and cannot use Tier USD05.",
            exception.message,
        )
    }

    @Test
    fun `valid path is accepted and changed`() {
        val user = testUser()
            .also { it.creator.currency = Currency.EUR }
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(name = "Andy Muller", bio = "New life", tierId = "EUR10").toJson())

        every { tierRepository.get("EUR10") } returns Tier.ofId("EUR10")

        controller.routePatchUser(request)
        assertEquals("Andy Muller", user.name)
        assertEquals("New life", user.bio)
        assertEquals("<strong>New life</strong>", user.bioHtml)
        assertEquals("New life in en", user.bioEn)
        assertEquals("<strong>New life in en</strong>", user.bioHtmlEn)
        assertEquals("EUR10", user.creator.tierId)
        // path timestamp must not be changed
        assertEquals(testUser().pathChanged, user.pathChanged)
        assertEquals(true, user.hasRssFeed)
    }

    @Test
    fun `incorrect phone is refused`() {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(phone = "555454").toJson())

        val exception = assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
        assertTrue("Phone must be numeric" in exception.message!!)
    }

    @Test
    fun `correct phone is accepted`() {
        val testPhone = "+*********** 555"
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(phone = testPhone).toJson())

        controller.routePatchUser(request)
        assertEquals(testPhone, user.company!!.phone)
    }

    @Test
    fun `correct birthdate is accepted`() {
        val testBirthDate = "2000-01-01"
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(birthDate = testBirthDate).toJson())

        controller.routePatchUser(request)
        assertEquals(testBirthDate, user.company!!.birthDate)
    }

    @Test
    fun `old birthdate is denied`() {
        val testBirthDate = "1901-01-01"
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(birthDate = testBirthDate).toJson())

        assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
    }

    @Test
    fun `user must be at least 13 years old`() {
        val testBirthDate = LocalDate.now().minusYears(12)
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(birthDate = testBirthDate.toString()).toJson())

        assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
    }

    @Test
    fun `null vatId is accepted`() {
        val testVatId = "DE123456"
        val user = testUser()
        user.company!!.vatId = testVatId
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(vatId = null).toJson())

        controller.routePatchUser(request)

        assertNull(user.company!!.vatId)
    }

    @ParameterizedTest
    @ValueSource(strings = ["CZ123456", "ES12AD3456", "DE123456789"])
    fun `valid vatId is accepted`(testVatId: String) {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(vatId = testVatId).toJson())

        controller.routePatchUser(request)

        assertEquals(testVatId, user.company!!.vatId)
    }

    @ParameterizedTest
    @ValueSource(strings = ["123456", "EU11", "EU123456", "EU"])
    fun `invalid vatId cannot be set`(testVatId: String) {
        val user = testUser()
        every { userRepository.get(any(), user.id) } returns user

        val request = Request(Method.PATCH, "/v2/users/${user.id}")
            .withAccessTokenCookie(jwtFor(user.id))
            .body(testBody(vatId = testVatId).toJson())

        assertThrows<BadRequestException> {
            controller.routePatchUser(request)
        }
    }
}
