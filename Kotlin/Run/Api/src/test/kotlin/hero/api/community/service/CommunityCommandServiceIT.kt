package hero.api.community.service

import hero.baseutils.minusHours
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.model.CommunityMemberStatus
import hero.model.CommunityType
import hero.model.ImageAsset
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.USER
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class CommunityCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Nested
    inner class CreateCommunity {
        @Test
        fun `should create a community for a user who can create communities`() {
            val communityRepository = CommunityRepository(testContext)
            val underTest = prepareService()

            val user = testHelper.createUser("filip")

            val community = underTest.execute(CreateCommunity("filip"))

            with(communityRepository.getById(community.id)) {
                assertThat(this).isEqualTo(community)
                assertThat(name).isEqualTo(user.name)
                assertThat(description).isEqualTo("Community")
                assertThat(slug).isEqualTo(user.path)
                assertThat(ownerId).isEqualTo("filip")
                assertThat(membersCount).isEqualTo(1)
                assertThat(image).isEqualTo(user.image)
                assertThat(createdAt).isEqualTo(expectedTimestamp)
                assertThat(updatedAt).isEqualTo(expectedTimestamp)
                assertThat(deletedAt).isNull()
            }

            val publishedEvent = slot<CommunityCreated>()
            verify { pubSubMock.publish(capture(publishedEvent)) }
            assertThat(publishedEvent.captured.communityId).isEqualTo(community.id)

            assertThat(testContext.selectFrom(USER).where(USER.ID.eq("filip")).fetchSingle().ownedCommunitiesCount)
                .isEqualTo(1)
        }

        @Test
        fun `user cannot create a community if they already have one`() {
            val underTest = prepareService()

            testHelper.createUser("filip")

            // Create first community
            underTest.execute(CreateCommunity("filip"))

            // Try to create second community
            assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
                underTest.execute(CreateCommunity("filip"))
            }.withMessage("User filip already has a community")
        }
    }

    @Nested
    inner class UpdateCommunity {
        @Test
        fun `should update community with given attributes`() {
            val communityRepository = CommunityRepository(testContext)
            val underTest = prepareService()

            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner")

            val newImage = ImageAsset(
                id = "https://example.com/new-image.jpg",
                width = 800,
                height = 600,
                fileName = null,
            )

            val updatedCommunity = underTest.execute(
                UpdateCommunity(
                    communityId = community.id,
                    userId = "owner",
                    name = "Updated Community Name",
                    description = "Updated community description",
                    image = newImage,
                    slug = "updatedslug",
                    type = CommunityType.FREE,
                ),
            )

            with(communityRepository.getById(community.id)) {
                assertThat(this).isEqualTo(updatedCommunity)
                assertThat(name).isEqualTo("Updated Community Name")
                assertThat(description).isEqualTo("Updated community description")
                assertThat(image).isEqualTo(newImage)
                assertThat(slug).isEqualTo("updatedslug")
                assertThat(ownerId).isEqualTo("owner")
                assertThat(membersCount).isEqualTo(community.membersCount)
                assertThat(threadsCount).isEqualTo(community.threadsCount)
                assertThat(createdAt).isEqualTo(community.createdAt)
                assertThat(slugUpdatedAt).isEqualTo(expectedTimestamp)
                assertThat(updatedAt).isEqualTo(expectedTimestamp)
                assertThat(deletedAt).isNull()
                assertThat(type).isEqualTo(CommunityType.FREE)
            }
        }

        @Test
        fun `should not set slugUpdatedAt if slug is not being updated`() {
            val communityRepository = CommunityRepository(testContext)
            val underTest = prepareService()

            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", slug = "slug")

            val updatedCommunity = underTest.execute(
                UpdateCommunity(
                    communityId = community.id,
                    userId = "owner",
                    name = "Updated Community Name",
                    description = "Updated community description",
                    image = null,
                    slug = community.slug,
                    type = CommunityType.CONNECTED,
                ),
            )

            with(communityRepository.getById(community.id)) {
                assertThat(this).isEqualTo(updatedCommunity)
                assertThat(name).isEqualTo("Updated Community Name")
                assertThat(description).isEqualTo("Updated community description")
                assertThat(image).isEqualTo(null)
                assertThat(slug).isEqualTo("slug")
                assertThat(ownerId).isEqualTo("owner")
                assertThat(membersCount).isEqualTo(community.membersCount)
                assertThat(threadsCount).isEqualTo(community.threadsCount)
                assertThat(createdAt).isEqualTo(community.createdAt)
                assertThat(slugUpdatedAt).isNull()
                assertThat(updatedAt).isEqualTo(expectedTimestamp)
                assertThat(deletedAt).isNull()
            }
        }

        @Test
        fun `user cannot update community they do not own`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            testHelper.createUser("other")
            val community = testHelper.createCommunity("owner")

            assertThatExceptionOfType(ForbiddenException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "other",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "hackedslug",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("User other cannot update community ${community.id}")
        }

        @Test
        fun `should throw conflict exception if another community has given slug`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            testHelper.createUser("other")
            val community = testHelper.createCommunity("owner", slug = "slug-1")
            testHelper.createCommunity("other", slug = "slug2")

            assertThatExceptionOfType(ConflictException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "slug2",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("Community with slug slug2 already exists")
        }

        @Test
        fun `should throw conflict exception if another user has given slug`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            testHelper.createUser("other", path = "otheruserpath")
            val community = testHelper.createCommunity("owner", slug = "slug")

            assertThatExceptionOfType(ConflictException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "otheruserpath",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("User with slug otheruserpath already exists")
        }

        @Test
        fun `owner can set slug to their own path`() {
            val underTest = prepareService()
            testHelper.createUser("owner", path = "owner-path")
            val community = testHelper.createCommunity("owner", slug = "slug")

            val updatedCommunity = underTest.execute(
                UpdateCommunity(
                    communityId = community.id,
                    userId = "owner",
                    name = community.name,
                    description = community.description,
                    image = community.image,
                    slug = "ownerpath",
                    type = CommunityType.CONNECTED,
                ),
            )

            assertThat(updatedCommunity).isEqualTo(
                community.copy(slug = "ownerpath", updatedAt = expectedTimestamp, slugUpdatedAt = expectedTimestamp),
            )
        }

        @Test
        fun `should throw if slug is too short`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", slug = "slug-1", slugUpdatedAt = Instant.now())

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "s",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("Slug must be at least 3 characters long")
        }

        @Test
        fun `should throw if slug is not lower alphanumeric`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", slug = "slug-1", slugUpdatedAt = Instant.now())

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "aBC13",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("Slug must contain only lowercase letters and numbers")
        }

        @Test
        fun `should throw conflict exception if user changes slug too often`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", slug = "slug-1", slugUpdatedAt = Instant.now())

            assertThatExceptionOfType(ConflictException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCommunity(
                            communityId = community.id,
                            userId = "owner",
                            name = "Hacked Name",
                            description = "Hacked description",
                            image = null,
                            slug = "slug2",
                            type = CommunityType.CONNECTED,
                        ),
                    )
                }
                .withMessage("Community slug can be changed once in an hour")
        }

        @Test
        fun `user can change the slug after an hour`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", slug = "s", slugUpdatedAt = Instant.now().minusHours(2))

            val result = underTest.execute(
                UpdateCommunity(
                    communityId = community.id,
                    userId = "owner",
                    name = community.name,
                    description = community.description,
                    image = null,
                    slug = "slug2",
                    type = CommunityType.CONNECTED,
                ),
            )

            assertThat(result).isEqualTo(
                community.copy(updatedAt = expectedTimestamp, slugUpdatedAt = expectedTimestamp, slug = "slug2"),
            )
        }
    }

    @Nested
    inner class JoinCommunity {
        @Test
        fun `should add user to a community if he was not member before`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)

            testHelper.createUser("pablo")

            underTest.execute(JoinCommunity("pablo", community.id))

            val createdCommunityMember = testContext.selectFrom(Tables.COMMUNITY_MEMBER).fetchSingle()

            assertThat(createdCommunityMember.state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(createdCommunityMember.userId).isEqualTo("pablo")

            assertThat(TestRepositories.communityRepository.getById(community.id).membersCount).isEqualTo(1)
        }

        @Test
        fun `should add user to a community if he was a member before`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)
            testHelper.createUser("pablo")
            testHelper.createCommunityMember(community.id, "pablo", CommunityMemberStatus.LEFT)

            underTest.execute(JoinCommunity("pablo", community.id))

            val createdCommunityMember = testContext.selectFrom(Tables.COMMUNITY_MEMBER).fetchSingle()

            assertThat(createdCommunityMember.state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(createdCommunityMember.userId).isEqualTo("pablo")

            assertThat(TestRepositories.communityRepository.getById(community.id).membersCount).isEqualTo(1)
        }

        @Test
        fun `should throw if already a member`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)
            testHelper.createUser("pablo")
            testHelper.createCommunityMember(community.id, "pablo", CommunityMemberStatus.ACTIVE)

            assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
                underTest.execute(JoinCommunity("pablo", community.id))
            }
        }

        @Test
        fun `cannot join a non free community`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.CONNECTED)

            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(JoinCommunity("pablo", community.id))
            }
        }
    }

    @Nested
    inner class LeaveCommunity {
        @Test
        fun `should remove user from a community`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)
            testHelper.createUser("pablo")
            testHelper.createCommunityMember(community.id, "pablo", CommunityMemberStatus.ACTIVE)

            underTest.execute(LeaveCommunity("pablo", community.id))

            val removedCommunityMember = testContext.selectFrom(Tables.COMMUNITY_MEMBER).fetchSingle()

            assertThat(removedCommunityMember.state).isEqualTo(CommunityMemberStatus.LEFT.name)
            assertThat(removedCommunityMember.userId).isEqualTo("pablo")

            assertThat(TestRepositories.communityRepository.getById(community.id).membersCount).isEqualTo(0)
        }

        @Test
        fun `owner cannot leave his own community`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(LeaveCommunity("owner", community.id))
                }
                .withMessage("Community owner cannot leave the community")
        }

        @Test
        fun `only active users can leave`() {
            val underTest = prepareService()
            testHelper.createUser("owner")
            val community = testHelper.createCommunity("owner", type = CommunityType.FREE)
            testHelper.createUser("pablo")
            testHelper.createCommunityMember(community.id, "pablo", CommunityMemberStatus.LEFT)

            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(LeaveCommunity("pablo", community.id))
                }
                .withMessage("User pablo is not an active member of community ${community.id}")
        }
    }

    private fun prepareService(): CommunityCommandService {
        val communityRepository = CommunityRepository(testContext)
        return CommunityCommandService(
            communityRepository = communityRepository,
            userRepository = TestRepositories.userRepository,
            pubSub = pubSubMock,
            lazyContext = lazyTestContext,
            clock = TestClock(expectedTimestamp),
            logger = TestLogger,
        )
    }
}
