package hero.api.subscriber.service

import hero.api.subscriber.repository.SubscribersSort.GIFTED_FIRST
import hero.api.subscriber.repository.SubscribersSort.HIGHEST_PRICE
import hero.api.subscriber.repository.SubscribersSort.LOWEST_PRICE
import hero.api.subscriber.repository.SubscribersSort.NEWEST
import hero.api.subscriber.repository.SubscribersSort.OLDEST
import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.exceptions.http.ForbiddenException
import hero.model.SubscriberStatus.ACTIVE
import hero.model.SubscriberStatus.CANCELLED
import hero.model.Tier
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class SubscriberQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetSubscribers {
        @Test
        fun `should return creator's active subscribers`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", tierId = "EUR08")

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", tierId = "EUR10")

            testHelper.createSubscriber("creator", "inactive", status = CANCELLED)

            val result = underTest.execute(GetSubscribers("creator", "creator", false, PageRequest()))

            assertThat(result.content).containsExactlyInAnyOrder(
                Subscription(subscriber1, user1, creator, Tier.ofId("EUR08")),
                Subscription(subscriber2, user2, creator, Tier.ofId("EUR10")),
            )
            assertThat(result.hasNext).isFalse()
        }

        @Test
        fun `should correctly list creator's expired and active subscribers`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", tierId = "EUR07", status = ACTIVE)

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber(
                "creator",
                "subscriber2",
                tierId = "EUR02",
                status = CANCELLED,
            )

            val user3 = testHelper.createUser("subscriber3")
            val subscriber3 = testHelper.createSubscriber(
                "creator",
                "subscriber3",
                tierId = "EUR01",
                status = CANCELLED,
            )

            val user4 = testHelper.createUser("subscriber4")
            val subscriber4 = testHelper.createSubscriber("creator", "subscriber4", tierId = "EUR05", status = ACTIVE)

            val pageable = PageRequest(sort = Sort.by(LOWEST_PRICE))

            val expired = underTest.execute(GetSubscribers("creator", "creator", true, pageable))
            assertThat(expired.content).containsExactly(
                Subscription(subscriber3, user3, creator, Tier.ofId("EUR01")),
                Subscription(subscriber2, user2, creator, Tier.ofId("EUR02")),
            )

            val notExpired = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(notExpired.content).containsExactly(
                Subscription(subscriber4, user4, creator, Tier.ofId("EUR05")),
                Subscription(subscriber1, user1, creator, Tier.ofId("EUR07")),
            )
        }

        @Test
        fun `should not allow non-subscribers to view creator's subscribers`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            testHelper.createUser("creator")
            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetSubscribers("creator", "someone-else", false, PageRequest()))
            }
        }

        @Test
        fun `should not allow inactive subscribers to view creator's subscribers`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            testHelper.createUser("creator")
            testHelper.createSubscriber("creator", "inactive", status = CANCELLED)
            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetSubscribers("creator", "inactive", false, PageRequest()))
            }
        }
    }

    @Nested
    inner class GetSubscriptions {
        @Test
        fun `should return user's active subscriptions`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val user = testHelper.createUser("user")

            val creator1 = testHelper.createUser("creator1")
            val creator2 = testHelper.createUser("creator2")

            val subscriber1 = testHelper.createSubscriber("creator1", "user")
            val subscriber2 = testHelper.createSubscriber("creator2", "user")

            testHelper.createSubscriber("creator3", "user", status = CANCELLED)
            testHelper.createSubscriber("creator4", "user", status = CANCELLED)

            val result = underTest.execute(GetSubscriptions("user", "user", false, PageRequest()))

            assertThat(result.content).containsExactlyInAnyOrder(
                Subscription(subscriber1, user, creator1, Tier.ofId("EUR10")),
                Subscription(subscriber2, user, creator2, Tier.ofId("EUR10")),
            )
            assertThat(result.hasNext).isFalse()
        }

        @Test
        fun `should correctly list user's expired and active subscriptions`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val user = testHelper.createUser("user")

            val creator1 = testHelper.createUser("creator1")
            val subscriber1 = testHelper.createSubscriber("creator1", "user", tierId = "EUR07", status = ACTIVE)

            val creator2 = testHelper.createUser("creator2")
            val subscriber2 = testHelper.createSubscriber("creator2", "user", tierId = "EUR02", status = CANCELLED)

            val creator3 = testHelper.createUser("creator3")
            val subscriber3 = testHelper.createSubscriber("creator3", "user", tierId = "EUR01", status = CANCELLED)

            val creator4 = testHelper.createUser("creator4")
            val subscriber4 = testHelper.createSubscriber("creator4", "user", tierId = "EUR05", status = ACTIVE)

            val pageable = PageRequest(sort = Sort.by(LOWEST_PRICE))

            val expired = underTest.execute(GetSubscriptions("user", "user", true, pageable))
            assertThat(expired.content).containsExactly(
                Subscription(subscriber3, user, creator3, Tier.ofId("EUR01")),
                Subscription(subscriber2, user, creator2, Tier.ofId("EUR02")),
            )

            val notExpired = underTest.execute(GetSubscriptions("user", "user", false, pageable))
            assertThat(notExpired.content).containsExactly(
                Subscription(subscriber4, user, creator4, Tier.ofId("EUR05")),
                Subscription(subscriber1, user, creator1, Tier.ofId("EUR07")),
            )
        }

        @Test
        fun `should not allow non-subscribers to view creator's subscriptions`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            testHelper.createUser("creator")
            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetSubscriptions("creator", "someone-else", false, PageRequest()))
            }
        }

        @Test
        fun `should not allow inactive subscribers to view creator's subscriptions`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            testHelper.createUser("creator")
            testHelper.createSubscriber("creator", "inactive", status = CANCELLED)
            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetSubscriptions("creator", "inactive", false, PageRequest()))
            }
        }
    }

    @Nested
    inner class GetSubscription {
        @Test
        fun `should return user's subscription for given creator`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val user = testHelper.createUser("user")
            val creator1 = testHelper.createUser("creator1")

            val subscriber1 = testHelper.createSubscriber("creator1", "user")
            testHelper.createSubscriber("creator2", "user", status = CANCELLED)

            val result = underTest.execute(GetSubscription("user", "creator1"))
            assertThat(result).isEqualTo(Subscription(subscriber1, user, creator1, Tier.ofId("EUR10")))
        }
    }

    @Nested
    inner class Pagination {
        @Test
        fun `should correctly page subscribers and sort by NEWEST`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")
            val now = Instant.now()

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", subscribedAt = now - 5.seconds)

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", subscribedAt = now - 10.seconds)

            val pageable = PageRequest(pageSize = 1, sort = Sort.by(NEWEST))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(result1.content).containsExactly(Subscription(subscriber1, user1, creator, Tier.ofId("EUR10")))
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber2, user2, creator, Tier.ofId("EUR10")))
            assertThat(result2.hasNext).isFalse()
        }

        @Test
        fun `should correctly page subscribers and sort by OLDEST`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")
            val now = Instant.now()

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", subscribedAt = now - 5.seconds)

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", subscribedAt = now - 10.seconds)

            val pageable = PageRequest(pageSize = 1, sort = Sort.by(OLDEST))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))

            assertThat(result1.content).containsExactly(Subscription(subscriber2, user2, creator, Tier.ofId("EUR10")))
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber1, user1, creator, Tier.ofId("EUR10")))
            assertThat(result2.hasNext).isFalse()
        }

        @Test
        fun `should correctly page subscribers and sort by LOWEST_PRICE`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", tierId = "EUR05")

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", tierId = "EUR01")

            val user3 = testHelper.createUser("subscriber3")
            val subscriber3 = testHelper.createSubscriber("creator", "subscriber3", tierId = "EUR02")

            val user4 = testHelper.createUser("subscriber4")
            val subscriber4 = testHelper.createSubscriber("creator", "subscriber4", tierId = "EUR07")

            val pageable = PageRequest(pageSize = 1, sort = Sort.by(LOWEST_PRICE))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(result1.content).containsExactly(Subscription(subscriber2, user2, creator, Tier.ofId("EUR01")))
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber3, user3, creator, Tier.ofId("EUR02")))
            assertThat(result2.hasNext).isTrue()

            val result3 = underTest.execute(GetSubscribers("creator", "creator", false, result2.nextPageable))
            assertThat(result3.content).containsExactly(Subscription(subscriber1, user1, creator, Tier.ofId("EUR05")))
            assertThat(result3.hasNext).isTrue()

            val result4 = underTest.execute(GetSubscribers("creator", "creator", false, result3.nextPageable))
            assertThat(result4.content).containsExactly(Subscription(subscriber4, user4, creator, Tier.ofId("EUR07")))
            assertThat(result4.hasNext).isFalse()
        }

        @Test
        fun `should correctly page subscribers and sort by HIGHEST_PRICE`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", tierId = "EUR05")

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", tierId = "EUR01")

            val user3 = testHelper.createUser("subscriber3")
            val subscriber3 = testHelper.createSubscriber("creator", "subscriber3", tierId = "EUR02")

            val user4 = testHelper.createUser("subscriber4")
            val subscriber4 = testHelper.createSubscriber("creator", "subscriber4", tierId = "EUR07")

            val pageable = PageRequest(pageSize = 1, sort = Sort.by(HIGHEST_PRICE))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(result1.content).containsExactly(Subscription(subscriber4, user4, creator, Tier.ofId("EUR07")))
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber1, user1, creator, Tier.ofId("EUR05")))
            assertThat(result2.hasNext).isTrue()

            val result3 = underTest.execute(GetSubscribers("creator", "creator", false, result2.nextPageable))
            assertThat(result3.content).containsExactly(Subscription(subscriber3, user3, creator, Tier.ofId("EUR02")))
            assertThat(result3.hasNext).isTrue()

            val result4 = underTest.execute(GetSubscribers("creator", "creator", false, result3.nextPageable))
            assertThat(result4.content).containsExactly(Subscription(subscriber2, user2, creator, Tier.ofId("EUR01")))
            assertThat(result4.hasNext).isFalse()
        }

        @Test
        fun `should correctly page subscribers and sort by GIFTED_FIRST`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber("creator", "subscriber1", couponAppliedForMonths = 3)

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", tierId = "EUR05")

            val pageable = PageRequest(pageSize = 1, sort = Sort.by(GIFTED_FIRST))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(result1.content).containsExactly(Subscription(subscriber1, user1, creator, Tier.ofId("EUR10")))
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber2, user2, creator, Tier.ofId("EUR05")))
            assertThat(result2.hasNext).isFalse()
        }

        @Test
        fun `should correctly page subscribers and sort by GIFTED_FIRST, result contains coupon and non-coupon`() {
            val underTest = SubscriberQueryService(
                TestCollections.subscribersCollection,
                TestCollections.usersCollection,
            )
            val creator = testHelper.createUser("creator")
            val now = Instant.now()

            val user1 = testHelper.createUser("subscriber1")
            val subscriber1 = testHelper.createSubscriber(
                "creator",
                "subscriber1",
                couponAppliedForMonths = 3,
                subscribedAt = now - 30.seconds,
            )

            val user2 = testHelper.createUser("subscriber2")
            val subscriber2 = testHelper.createSubscriber("creator", "subscriber2", subscribedAt = now - 5.seconds)

            val user3 = testHelper.createUser("subscriber3")
            val subscriber3 = testHelper.createSubscriber("creator", "subscriber3", subscribedAt = now - 10.seconds)

            val pageable = PageRequest(pageSize = 2, sort = Sort.by(GIFTED_FIRST))
            val result1 = underTest.execute(GetSubscribers("creator", "creator", false, pageable))
            assertThat(result1.content).containsExactly(
                Subscription(subscriber1, user1, creator, Tier.ofId("EUR10")),
                Subscription(subscriber2, user2, creator, Tier.ofId("EUR10")),
            )
            assertThat(result1.hasNext).isTrue()

            val result2 = underTest.execute(GetSubscribers("creator", "creator", false, result1.nextPageable))
            assertThat(result2.content).containsExactly(Subscription(subscriber3, user3, creator, Tier.ofId("EUR10")))
            assertThat(result2.hasNext).isFalse()
        }
    }
}
