package hero.api.user.service

import hero.api.statistics.service.SubscriberStatisticsQueryService
import hero.exceptions.http.NotFoundException
import hero.model.UserStatus
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class UserQueryServiceIT : IntegrationTest() {
    @Nested
    inner class FindUserByEmail {
        @Test
        fun `should fetch user by an email`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            val user = testHelper.createUser("filip", email = "<EMAIL>")

            val foundUser = underTest.execute(FindUserByEmail("<EMAIL>"))

            assertThat(foundUser).isEqualTo(user)
        }

        @Test
        fun `should return empty if the user with given email does not exist`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            testHelper.createUser("filip", email = "<EMAIL>")

            val foundUser = underTest.execute(FindUserByEmail("<EMAIL>"))

            assertThat(foundUser).isNull()
        }
    }

    @Nested
    inner class GetUser {
        @Test
        fun `should fetch user by id with categories`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            val user = testHelper.createUser("filip")
            val category = testHelper.createCategory("filip")

            val result = underTest.execute(GetUser("filip"))

            assertThat(result.user).isEqualTo(user)
            assertThat(result.categories).containsExactly(category)
        }

        @Test
        fun `should try to find user by path - ignoring the case`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            val user = testHelper.createUser("filip")
            val category = testHelper.createCategory("filip")
            testHelper.createPath("filippath", "filip", null)

            val result = underTest.execute(GetUser("filipPath"))
            assertThat(result.user).isEqualTo(user)
            assertThat(result.categories).containsExactly(category)
        }

        @Test
        fun `should throw if the user is deleted`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            testHelper.createUser("filip", status = UserStatus.DELETED)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetUser("filip"))
            }
        }
    }

    @Nested
    inner class GetUserDetails {
        @Test
        fun `should fetch user by id with categories`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            val user = testHelper.createUser("filip")
            val category = testHelper.createCategory("filip")

            val result = underTest.execute(GetUserDetails("filip"))

            assertThat(result.user).isEqualTo(user)
            assertThat(result.categories).containsExactly(category)
        }

        @Test
        fun `should throw if the user is deleted`() {
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )
            testHelper.createUser("filip", status = UserStatus.DELETED)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetUserDetails("filip"))
            }
        }

        @Disabled("Incomes are no longer dynamically fetched")
        @Test
        fun `should fetch incomes clean from the database`() {
            val subscriberStatisticsQueryService = mockk<SubscriberStatisticsQueryService>()
            val underTest = UserQueryService(
                TestRepositories.userRepository,
                TestCollections.categoriesCollection,
                TestCollections.pathsCollection,
                TestLogger,
            )

            val user = testHelper.createUser("filip")
            val category = testHelper.createCategory("filip")

            val result = underTest.execute(GetUserDetails("filip"))

            assertThat(result.user).isEqualTo(user.copy(counts = user.counts.copy(incomesClean = 90, incomes = 100)))
            assertThat(result.categories).containsExactly(category)

            verify(exactly = 1) { subscriberStatisticsQueryService.execute(any()) }
        }
    }
}
