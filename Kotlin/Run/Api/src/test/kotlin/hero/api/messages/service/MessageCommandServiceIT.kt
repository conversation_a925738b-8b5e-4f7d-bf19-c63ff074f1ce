package hero.api.messages.service

import hero.api.gjirafaAsset
import hero.api.post.service.PostService
import hero.contract.api.dto.GjirafaAssetInput
import hero.contract.api.dto.ImageAssetInput
import hero.contract.api.dto.PostAssetInput
import hero.exceptions.http.ForbiddenException
import hero.gjirafa.GjirafaUploadsService
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.ImageAsset
import hero.model.PostAsset
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import java.time.Instant

class MessageCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Test
    fun `should send a message to a thread`() {
        val gjirafaUploadsService = mockk<GjirafaUploadsService>()
        val gjirafaAsset = gjirafaAsset("id")
        every { gjirafaUploadsService.getAsset(any(), any(), any()) } returns gjirafaAsset

        val underTest = prepareService(gjirafaUploadsService)

        testHelper.createUser("filip")
        testHelper.createMessageThread("filip", listOf("pablo"), id = "message-thread-id")

        val inputs = listOf(
            PostAssetInput(image = ImageAssetInput("https://image.com", 100, 500, "fileName", 150)),
            PostAssetInput(gjirafa = GjirafaAssetInput("id")),
            PostAssetInput(document = DocumentAsset("document-url", DocumentType.DOCX, "name", 100)),
        )
        val message = underTest.execute(SendMessage("filip", "message-thread-id", "text", inputs, 100))

        val storedMessage = TestRepositories.postRepository.getById(message.id)

        assertThat(storedMessage).isEqualTo(message)
        assertThat(storedMessage.text).isEqualTo("text")
        assertThat(storedMessage.userId).isEqualTo("filip")
        assertThat(storedMessage.messageThreadId).isEqualTo("message-thread-id")
        assertThat(storedMessage.published).isEqualTo(expectedTimestamp)
        assertThat(storedMessage.parentId).isNull()
        assertThat(storedMessage.siblingId).isNull()
        assertThat(storedMessage.parentPostId).isNull()
        assertThat(storedMessage.parentUserId).isNull()
        assertThat(storedMessage.assets).containsExactly(
            PostAsset(image = ImageAsset.of("https://image.com", 100, 500, "fileName", 150)),
            PostAsset(gjirafa = gjirafaAsset),
            PostAsset(document = DocumentAsset("document-url", DocumentType.DOCX, "name", 100)),
        )
        assertThat(storedMessage.price).isEqualTo(100)

        val messageRecord = testContext.selectFrom(Tables.POST).where(Tables.POST.ID.eq(message.id)).fetchSingle()
        assertThat(messageRecord.type).isEqualTo("MESSAGE")

        with(TestCollections.messageThreadsCollection["message-thread-id"].get()) {
            assertThat(lastMessageAt).isEqualTo(expectedTimestamp)
            assertThat(lastMessageBy).isEqualTo("filip")
            assertThat(lastMessageId).isEqualTo(message.id)
            assertThat(seens).containsEntry("filip", expectedTimestamp)
            assertThat(checks).containsEntry("filip", expectedTimestamp)
        }

        verify(exactly = 1) { gjirafaUploadsService.getAsset(null, "id", false) }
    }

    @Test
    fun `user cannot send a message if he is not allowed to send messages to the thread`() {
        val underTest = prepareService()

        testHelper.createUser("filip")
        testHelper.createMessageThread(
            "filip",
            listOf("pablo"),
            id = "message-thread-id",
            canMessage = mapOf("filip" to false, "pablo" to true),
        )

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest.execute(SendMessage("filip", "message-thread-id", "text", listOf(), null))
        }
    }

    @Test
    fun `sending message to archived or deleted thread should reactivate the thread`() {
        val underTest = prepareService()

        testHelper.createUser("filip")
        testHelper.createMessageThread("filip", listOf("pablo"), id = "message-thread-id", deletedFor = listOf("filip"))

        underTest.execute(SendMessage("filip", "message-thread-id", "text", listOf(), null))

        with(TestCollections.messageThreadsCollection["message-thread-id"].get()) {
            assertThat(deletedFor).isEmpty()
            assertThat(activeFor).containsExactlyInAnyOrder("filip", "pablo")
        }
    }

    private fun prepareService(gjirafaService: GjirafaUploadsService = mockk()): MessageCommandService {
        val testClock = TestClock(expectedTimestamp)
        return MessageCommandService(
            TestCollections.messageThreadsCollection,
            PostService(
                postsCollection = TestCollections.postsCollection,
                postRepository = TestRepositories.postRepository,
                gjirafaService = gjirafaService,
                gjirafaLivestreamService = mockk(),
                pubSub = pubSubMock,
                clock = testClock,
            ),
            TestRepositories.userRepository,
            mockk(),
            lazyTestContext,
            testClock,
        )
    }
}
