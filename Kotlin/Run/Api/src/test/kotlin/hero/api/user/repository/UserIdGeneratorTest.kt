package hero.api.user.repository

import hero.baseutils.mockNow
import hero.model.Creator
import hero.model.User
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.Random
import kotlin.test.assertEquals

class UserIdGeneratorTest {
    @BeforeEach
    fun beforeEach() {
        mockNow("2023-09-02T20:00:00Z")
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    fun `path and ids are generated`() {
        val userIdGenerator = UserIdGenerator(Random(0))
        // j(onas) -> j(oyous)
        assertEquals("joyous", userIdGenerator.generatePath("jonas"))
        assertEquals("nfdvbaapmfq", userIdGenerator.generateId("jonas"))

        // v(ojtech) -> v(ivacious)
        assertEquals("vivacious", userIdGenerator.generatePath("vojtech"))
        assertEquals("ehofcaapmfq", userIdGenerator.generateId("vojtech"))

        // e(ster) -> e(xemplary)
        assertEquals("exemplary", userIdGenerator.generatePath("ester"))
        assertEquals("iwofnaapmfq", userIdGenerator.generateId("ester"))

        // h(ung) -> h(onest)
        assertEquals("honest", userIdGenerator.generatePath("hung"))
        assertEquals("vgywbaapmfq", userIdGenerator.generateId("hung"))

        // (empty) -> a(wesome)
        assertEquals("awesome", userIdGenerator.generatePath(""))
        assertEquals("bmyasaapmfq", userIdGenerator.generateId(""))

        // (non-alpha) -> a(ttentive)
        assertEquals("attentive", userIdGenerator.generatePath("5645646"))
        assertEquals("akitgaapmfq", userIdGenerator.generateId("5645646"))

        // seeds starting with 'x' and 'y' should not fail
        assertEquals("yenning", userIdGenerator.generatePath("Yolozilla"))
        assertEquals("xanthic", userIdGenerator.generatePath("Xaver121"))
    }

    @Test
    fun `adjective list contains all letters from a-z`() {
        val adjectivesFirstLetters = javaClass.classLoader
            .getResourceAsStream("adjectives.list")
            ?.bufferedReader()
            ?.useLines { lines -> lines.toList() }
            ?.mapNotNull { it.firstOrNull()?.lowercase() }
            ?.toSet()
            ?: emptySet()

        val alphabetSet = ('a'..'z').map { it.toString() }.toSet()
        assertEquals(alphabetSet, adjectivesFirstLetters)
    }

    @Test
    fun `new user id is denied if found duplicate`() {
        val userIdGenerator = UserIdGenerator(Random(0))
        val userRepository = spyk(
            UsersRepository(
                firestoreFulltext = mockk(),
                imageRepository = mockk(),
                pathCollection = mockk(),
                pubSub = mockk(),
                subscriberCollection = mockk(),
                collection = mockk(),
                userIdGenerator = userIdGenerator,
                accountService = mockk(),
                userRepository = mockk(),
            ),
        )
        every { userRepository.find("nfdvbaapmfq") } returns User(
            id = "random-id",
            path = "random",
            name = "random",
            creator = Creator(tierId = "EUR05"),
        )
        val exception = assertThrows<IllegalStateException> {
            userRepository.generateUserIdVerified("jonas")
        }
        assertEquals(
            "Duplicate user id 'nfdvbaapmfq' for a new User was generated. Will be retried.",
            exception.message,
        )
    }
}
