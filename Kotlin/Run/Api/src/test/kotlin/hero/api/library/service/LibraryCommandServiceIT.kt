package hero.api.library.service

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.fetchAll
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class LibraryCommandServiceIT : IntegrationTest() {
    @Nested
    inner class AddPostToLibrary {
        @Test
        fun `should add a post to user's library`() {
            val underTest = LibraryCommandService(
                TestCollections.savedPostsCollection,
                TestCollections.postsCollection,
                TestCollections.subscribersCollection,
                TestLogger,
            )
            val post = testHelper.createPost(userId = "cestmir")
            testHelper.createSubscriber(creatorId = "cestmir", userId = "pavel")

            val result = underTest.execute(AddPostToLibrary(post.id, "pavel"))
            val savedPost = TestCollections.savedPostsCollection.fetchAll()

            assertThat(savedPost)
                .usingRecursiveFieldByFieldElementComparatorIgnoringFields("savedAt", "postPublishedAt")
                .containsExactly(result.entity)
        }

        @Test
        fun `should not add a post to user's library if he is not subscribed to the post's creator`() {
            val underTest = LibraryCommandService(
                TestCollections.savedPostsCollection,
                TestCollections.postsCollection,
                TestCollections.subscribersCollection,
                TestLogger,
            )
            val post = testHelper.createPost(userId = "cestmir")

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(AddPostToLibrary(post.id, "pavel"))
            }
        }

        @Test
        fun `post creator should always be able to add his posts to his library`() {
            val underTest = LibraryCommandService(
                TestCollections.savedPostsCollection,
                TestCollections.postsCollection,
                TestCollections.subscribersCollection,
                TestLogger,
            )
            val post = testHelper.createPost(userId = "cestmir")

            val result = underTest.execute(AddPostToLibrary(post.id, "cestmir"))
            val savedPost = TestCollections.savedPostsCollection.fetchAll()

            assertThat(savedPost)
                .usingRecursiveFieldByFieldElementComparatorIgnoringFields("savedAt", "postPublishedAt")
                .containsExactly(result.entity)
        }
    }

    @Nested
    inner class RemovePostFromLibrary {
        @Test
        fun `should remove saved post from user's library`() {
            val underTest = LibraryCommandService(
                TestCollections.savedPostsCollection,
                TestCollections.postsCollection,
                TestCollections.subscribersCollection,
                TestLogger,
            )
            val post = testHelper.createPost("cestmir")
            testHelper.createUser("alex")
            val savedPost = testHelper.createSavedPost("alex", post)

            underTest.execute(RemovePostFromLibrary(post.id, "alex"))

            assertThat(TestCollections.savedPostsCollection[savedPost.id].fetch()).isNull()
        }

        @Test
        fun `user should not be able to delete from another user's library`() {
            val underTest = LibraryCommandService(
                TestCollections.savedPostsCollection,
                TestCollections.postsCollection,
                TestCollections.subscribersCollection,
                TestLogger,
            )
            val post = testHelper.createPost("cestmir")
            testHelper.createUser("alex")
            testHelper.createSavedPost("alex", post)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(RemovePostFromLibrary(post.id, "isaac"))
            }
        }
    }
}
