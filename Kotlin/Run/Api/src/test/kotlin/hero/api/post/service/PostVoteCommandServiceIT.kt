package hero.api.post.service

import hero.exceptions.http.ForbiddenException
import hero.model.CommunityType
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.jooq.DSLContext
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.Clock
import java.time.Instant

class PostVoteCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(**********)
    private val oldTimestamp = Instant.ofEpochSecond(**********)

    @ParameterizedTest
    @CsvSource(
        value = [
            "1",
            "0",
            "-1",
        ],
    )
    fun `should create a post vote (brand new)`(voteValue: Int) {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")

        underTest.execute(CastPostVote("cestmir", "thread-id", voteValue))

        val post = TestCollections.postsCollection["thread-id"].get()
        assertThat(post.voteScore).isEqualTo(voteValue.toLong())

        with(testContext.selectFrom(Tables.POST_VOTE).fetchSingle()) {
            assertThat(this.postId).isEqualTo("thread-id")
            assertThat(this.userId).isEqualTo("cestmir")
            assertThat(this.voteValue).isEqualTo(voteValue)
            assertThat(this.votedAt).isEqualTo(expectedTimestamp)
            assertThat(this.createdAt).isEqualTo(expectedTimestamp)
            assertThat(this.updatedAt).isEqualTo(expectedTimestamp)
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "1, 1, 0",
            "1, 0, -1",
            "1, -1, -2",

            "0, 1, 1",
            "0, 0, 0",
            "0, -1, -1",

            "-1, 1, 2",
            "-1, 0, 1",
            "-1, -1, 0",
        ],
    )
    fun `should update a post vote from an already existing vote`(
        oldValue: Int,
        newValue: Int,
        expectedValue: Int,
    ) {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")
        testContext.createPostVote("cestmir", "thread-id", oldValue)

        // we create another post to verify that the vote score is correctly incremented only for the given post
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id-another")
        testContext.createPostVote("cestmir", "thread-id-another", 1)

        underTest.execute(CastPostVote("cestmir", "thread-id", newValue))

        val post = TestCollections.postsCollection["thread-id"].get()
        assertThat(post.voteScore).isEqualTo(expectedValue.toLong())

        with(testContext.selectFrom(Tables.POST_VOTE).where(Tables.POST_VOTE.POST_ID.eq("thread-id")).fetchSingle()) {
            assertThat(this.postId).isEqualTo("thread-id")
            assertThat(this.userId).isEqualTo("cestmir")
            assertThat(this.voteValue).isEqualTo(voteValue)
            if (oldValue == newValue) {
                assertThat(this.createdAt).isEqualTo(oldTimestamp)
                assertThat(this.updatedAt).isEqualTo(oldTimestamp)
                assertThat(this.votedAt).isEqualTo(oldTimestamp)
            } else {
                assertThat(this.createdAt).isNotEqualTo(expectedTimestamp)
                assertThat(this.updatedAt).isEqualTo(expectedTimestamp)
            }
        }

        assertThat(TestCollections.postsCollection["thread-id-another"].get().voteScore).isEqualTo(0)
        with(
            testContext.selectFrom(Tables.POST_VOTE).where(Tables.POST_VOTE.POST_ID.eq("thread-id-another"))
                .fetchSingle(),
        ) {
            assertThat(this.voteValue).isEqualTo(1)
        }
    }

    @Test
    fun `only members can vote in non free communities`() {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir", type = CommunityType.CONNECTED)
        testHelper.createCommunityMember(community.id, "cestmir")
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest.execute(CastPostVote("petr", "thread-id", 1))
        }
    }

    @Test
    fun `everyone can vote in a free community`() {
        val underTest = prepareService()
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir", type = CommunityType.FREE)
        testHelper.createPost("cestmir", communityId = community.id, id = "thread-id")
        testHelper.createUser("petr")

        assertThatNoException().isThrownBy {
            underTest.execute(CastPostVote("petr", "thread-id", 1))
        }
    }

    private fun DSLContext.createPostVote(
        userId: String,
        postId: String,
        voteValue: Int,
    ) {
        insertInto(Tables.POST_VOTE)
            .set(Tables.POST_VOTE.USER_ID, userId)
            .set(Tables.POST_VOTE.POST_ID, postId)
            .set(Tables.POST_VOTE.VOTE_VALUE, voteValue)
            .set(Tables.POST_VOTE.CREATED_AT, oldTimestamp)
            .set(Tables.POST_VOTE.UPDATED_AT, oldTimestamp)
            .set(Tables.POST_VOTE.VOTED_AT, oldTimestamp)
            .execute()
    }

    private fun prepareService(testClock: Clock = TestClock(expectedTimestamp)): PostVoteCommandService =
        PostVoteCommandService(
            lazyTestContext,
            TestRepositories.postRepository,
            TestCollections.postsCollection,
            testClock,
        )
}
