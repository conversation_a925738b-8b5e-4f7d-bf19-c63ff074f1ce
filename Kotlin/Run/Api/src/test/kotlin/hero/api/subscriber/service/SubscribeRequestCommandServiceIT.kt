package hero.api.subscriber.service

import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.SupportCounts
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class SubscribeRequestCommandServiceIT : IntegrationTest() {
    @Nested
    inner class RequestToSubscribe {
        @Test
        fun `should create subscribe request`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTest<PERSON>ontext,
                Test<PERSON>lock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = underTest.execute(RequestToSubscribe("cestmir", "pablo"))

            val createdSubscribeRequest = TestRepositories.subscribeRequestRepository.getById(subscribeRequest.id)

            assertThat(subscribeRequest).isEqualTo(createdSubscribeRequest)
            assertThat(subscribeRequest.createdAt).isEqualTo(now)
            assertThat(subscribeRequest.userId).isEqualTo("pablo")
            assertThat(subscribeRequest.creatorId).isEqualTo("cestmir")
            assertThat(subscribeRequest.deletedAt).isNull()
            assertThat(subscribeRequest.declinedAt).isNull()
            assertThat(subscribeRequest.acceptedAt).isNull()
            assertThat(TestCollections.usersCollection["cestmir"].get().counts.pendingRequests).isEqualTo(1)
        }

        @Test
        fun `cannot create subscribe request if user already subscribes the creator`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")
            testHelper.createSubscription("pablo", "cestmir")

            assertThatExceptionOfType(ConflictException::class.java).isThrownBy {
                underTest.execute(RequestToSubscribe("cestmir", "pablo"))
            }
        }
    }

    @Nested
    inner class DeclineSubscribeRequest {
        @Test
        fun `should decline subscribe request`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir", counts = SupportCounts(pendingRequests = 10))
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
            )

            underTest.execute(DeclineSubscribeRequest("cestmir", subscribeRequest.id))

            val declinedRequest = TestRepositories.subscribeRequestRepository.getById(subscribeRequest.id)
            assertThat(declinedRequest.declinedAt).isEqualTo(now)
            assertThat(declinedRequest.acceptedAt).isNull()
            assertThat(declinedRequest.deletedAt).isNull()
            assertThat(TestCollections.usersCollection["cestmir"].get().counts.pendingRequests).isEqualTo(9)
        }

        @Test
        fun `cannot decline subscribe request if user is not the creator`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
            )

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(DeclineSubscribeRequest("other_user", subscribeRequest.id))
            }
        }

        @Test
        fun `cannot decline subscribe request if it is already accepted`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
                acceptedAt = now,
            )

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(DeclineSubscribeRequest("cestmir", subscribeRequest.id))
            }
        }

        @Test
        fun `cannot decline subscribe request if it is already declined`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
                declinedAt = now,
            )

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(DeclineSubscribeRequest("cestmir", subscribeRequest.id))
            }
        }
    }

    @Nested
    inner class CancelSubscribeRequest {
        @Test
        fun `should cancel subscribe request`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir", counts = SupportCounts(pendingRequests = 10))
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
            )

            underTest.execute(CancelSubscribeRequest("pablo", subscribeRequest.id))

            val canceledRequest = TestRepositories.subscribeRequestRepository.getById(subscribeRequest.id)
            assertThat(canceledRequest.deletedAt).isEqualTo(now)
            assertThat(canceledRequest.acceptedAt).isNull()
            assertThat(canceledRequest.declinedAt).isNull()
            assertThat(TestCollections.usersCollection["cestmir"].get().counts.pendingRequests).isEqualTo(9)
        }

        @Test
        fun `cannot cancel subscribe request if user is not the requester`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
            )

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(CancelSubscribeRequest("other_user", subscribeRequest.id))
            }
        }

        @Test
        fun `cannot cancel subscribe request if it is already accepted`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
                acceptedAt = now,
            )

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(CancelSubscribeRequest("pablo", subscribeRequest.id))
            }
        }

        @Test
        fun `cannot cancel subscribe request if it is already declined`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
                declinedAt = now,
            )

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(CancelSubscribeRequest("pablo", subscribeRequest.id))
            }
        }

        @Test
        fun `cannot cancel subscribe request if it is already canceled`() {
            val now = Instant.ofEpochSecond(1747831124)
            val underTest = SubscribeRequestCommandService(
                TestRepositories.subscribeRequestRepository,
                TestCollections.usersCollection,
                mockk(),
                mockk(),
                TestRepositories.notificationRepository,
                mockk(),
                lazyTestContext,
                TestClock(now),
            )
            testHelper.createUser("cestmir")
            testHelper.createUser("pablo")

            val subscribeRequest = testHelper.createSubscribeRequest(
                userId = "pablo",
                creatorId = "cestmir",
                createdAt = now,
                deletedAt = now,
            )

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(CancelSubscribeRequest("pablo", subscribeRequest.id))
            }
        }
    }
}
