package hero.api.user.service

import hero.model.GjirafaAsset
import hero.model.GjirafaStatus
import hero.model.PostAsset
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class UserMediaCommandServiceIT : IntegrationTest() {
    @Test
    fun `should create new media store and update timestamp for a video asset`() {
        val underTest = UserMediaCommandService(
            TestCollections.userStoresCollection,
            TestRepositories.postRepository,
        )

        val gjirafa = gjirafaAsset.copy(id = "vjsnpjvn", hasVideo = true, videoStreamUrl = "video-stream-url")
        val post = testHelper.createPost("anyone", assets = listOf(PostAsset(gjirafa = gjirafa)))

        underTest.execute(UpdateAssetTimestamp("cestmir", "vjsnpjvn", 14.28, post.id))

        assertThat(TestCollections.userStoresCollection["cestmir-media"].get().attributes.content)
            .isEqualTo(mapOf("video-stream-url" to 14.28))
    }

    @Test
    fun `should update existing media store and update timestamp only for given video asset`() {
        val underTest = UserMediaCommandService(
            TestCollections.userStoresCollection,
            TestRepositories.postRepository,
        )

        testHelper.createUserMediaStore("cestmir", mapOf("first-audio-url" to 72.3, "first-video-url" to 29.1))

        val gjirafa = gjirafaAsset.copy(id = "vjsnpjvn", hasVideo = true, videoStreamUrl = "first-video-url")
        val post = testHelper.createPost("anyone", assets = listOf(PostAsset(gjirafa = gjirafa)))

        underTest.execute(UpdateAssetTimestamp("cestmir", "vjsnpjvn", 112.3, post.id))

        assertThat(TestCollections.userStoresCollection["cestmir-media"].get().attributes.content)
            .isEqualTo(mapOf("first-audio-url" to 72.3, "first-video-url" to 112.3))
    }

    @Test
    fun `should create new media store and update timestamp for an audio asset`() {
        val underTest = UserMediaCommandService(
            TestCollections.userStoresCollection,
            TestRepositories.postRepository,
        )

        val gjirafa = gjirafaAsset.copy(id = "bbgjraork", hasVideo = false, audioStreamUrl = "audio-stream-url")
        val post = testHelper.createPost("anyone", assets = listOf(PostAsset(gjirafa = gjirafa)))

        underTest.execute(UpdateAssetTimestamp("cestmir", "bbgjraork", 49.92, post.id))

        assertThat(TestCollections.userStoresCollection["cestmir-media"].get().attributes.content)
            .isEqualTo(mapOf("audio-stream-url" to 49.92))
    }

    @Test
    fun `should update existing media store and update timestamp only for given audio asset`() {
        val underTest = UserMediaCommandService(
            TestCollections.userStoresCollection,
            TestRepositories.postRepository,
        )

        testHelper.createUserMediaStore("cestmir", mapOf("first-audio-url" to 72.3, "second-audio-url" to 29.1))

        val gjirafa = gjirafaAsset.copy(id = "bbgjraork", hasVideo = false, audioStreamUrl = "first-audio-url")
        val post = testHelper.createPost("anyone", assets = listOf(PostAsset(gjirafa = gjirafa)))

        underTest.execute(UpdateAssetTimestamp("cestmir", "bbgjraork", 112.3, post.id))

        assertThat(TestCollections.userStoresCollection["cestmir-media"].get().attributes.content)
            .isEqualTo(mapOf("first-audio-url" to 112.3, "second-audio-url" to 29.1))
    }
}

val gjirafaAsset = GjirafaAsset(
    key = "encode-key",
    audioByteSize = 0,
    audioStaticUrl = "audio-url",
    audioStreamUrl = "audio-stream",
    chaptersVttUrl = "chapters-vtt-url",
    thumbnailBlurhash = "LGSF;JIUofof00RjWBay4nofofj[",
    duration = 0.0,
    hasAudio = true,
    hasVideo = true,
    hidden = false,
    id = "id",
    status = GjirafaStatus.ERROR,
    encodingFinishTime = null,
    encodingRemainingTime = null,
    createdAt = Instant.now(),
    projectId = "project-id",
    imageKey = "123456789",
)
