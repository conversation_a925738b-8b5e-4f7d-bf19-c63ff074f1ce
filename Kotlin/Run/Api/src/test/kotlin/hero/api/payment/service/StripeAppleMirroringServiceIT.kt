package hero.api.payment.service

import com.stripe.param.PriceCreateParams
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.subscriber.repository.SubscribersRepository
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.SystemEnv
import hero.baseutils.instantOf
import hero.baseutils.systemEnv
import hero.gcloud.PubSub
import hero.gcloud.typedCollectionOf
import hero.model.AppleCharge
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeAccountService
import hero.stripe.service.StripeClients
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriptionService
import hero.stripe.service.VatMappingProvider
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.usersCollection
import hero.test.euStripeConnectedAccount
import hero.test.gcloud.FirestoreTestDatabase
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertNotNull
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StripeAppleMirroringServiceIT : IntegrationTest() {
    private val isProduction = false
    private val stripeAccountService: StripeAccountService = mockk()
    private val pubSub: PubSub = mockk(relaxed = true)
    private val tierRepository: TiersRepository = mockk()
    private val stripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs)
    private val pricingService = ApplePricingService()
    private val subscriberRepository: SubscribersRepository = SubscribersRepository(
        collection = firestore.typedCollectionOf(Subscriber),
    )
    private val tier = Tier.ofId("EUR05")

    private val stripeCouponService = StripeCouponService(stripeClients)

    private val userRepository: UsersRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = mockk(),
            pubSub = mockk(),
            subscriberCollection = mockk(),
            collection = usersCollection,
            userIdGenerator = mockk(),
            accountService = stripeAccountService,
            userRepository = mockk(),
        ),
    )

    private val stripeService = spyk(StripeService(stripeClients, pubSub))
    private val countryToVatMapping = VatMappingProvider(systemEnv("FLEXIBEE_PASSWORD")).countryToVatMapping()

    private val subscriptionService = StripeSubscriptionService(
        clients = stripeClients,
        paymentMethodsService = mockk(),
        production = isProduction,
        countryToVatMapping = countryToVatMapping,
        recurringPeriod = PriceCreateParams.Recurring.Interval.MONTH,
        stripeCouponService = stripeCouponService,
    )

    private val subscriberStripeRepository = spyk(
        SubscriberStripeRepository(
            stripe = stripeService,
            stripePricesCollection = firestore.typedCollectionOf(StripePrice),
            subscriberRepository = subscriberRepository,
            tierRepository = tierRepository,
            userRepository = userRepository,
            stripeSubscriptionService = subscriptionService,
            stripeCouponService = stripeCouponService,
            firestore = FirestoreTestDatabase.testFirestore,
            stripeSubscriberSaver = mockk(relaxed = true),
            lazyContext = lazyTestContext,
        ),
    )
    private val service = spyk(
        StripeAppleMirroringService(
            lazyTestContext,
            subscriberStripeRepository,
            stripeClients,
            userRepository,
            firestore.typedCollectionOf(AppleCharge),
            countryToVatMapping,
        ),
    )

    @Test
    fun `stripe subscription is created on apple callback`() {
        val user = testHelper.createUser()
        val creator = testHelper.createUser(tierId = tier.id, stripeAccountId = euStripeConnectedAccount)

        every { tierRepository.get(tier.id) } returns tier
        val appleReferenceId = UUID.randomUUID().toString()
        val appleTransactionId = UUID.randomUUID().toString()

        val currency = creator.creator.currency!!

        val response = service.createSubscription(
            appleReferenceId,
            appleTransactionId,
            user.id,
            creator.id,
            tier.id,
            currency,
            10_00,
            "CZE",
        )
        val refreshedUser = userRepository.get(user.id)
        assertEquals(Currency.EUR, currency)
        assertEquals(PaymentIntentStatus.SUCCEEDED, response.attributes.status)
        assertNotNull(response.attributes.stripeId)
        val stripeSubscription = stripeClients[currency].subscriptions().retrieve(response.attributes.stripeId)
        assertEquals(refreshedUser.customerIds[currency.name], stripeSubscription.customer)
        assertEquals(euStripeConnectedAccount, stripeSubscription.transferData.destination)
        assertEquals(CouponMethod.APPLE_IN_APP.name, stripeSubscription.discount.coupon.metadata["couponMethod"])
        assertEquals(CouponMethod.APPLE_IN_APP.name, stripeSubscription.metadata["couponMethod"])
        assertEquals(appleReferenceId, stripeSubscription.metadata["appleReferenceId"])
        assertEquals(appleTransactionId, stripeSubscription.metadata["appleTransactionId"])
        assertEquals(10_00L, stripeSubscription.metadata["applePriceCents"]?.toLongOrNull())
        assertEquals(currency.name, stripeSubscription.metadata["appleCurrency"])
    }

    @Test
    fun `stored apple charge in CZK correctly reflects hero subscription`() {
        val appleReferenceId = UUID.randomUUID().toString()
        val appleTransactionId = UUID.randomUUID().toString()
        val timestamp = instantOf("2025-01-01T00:00:00Z")

        val slot = slot<AppleCharge>()
        every { service.storeCharge(capture(slot)) } just runs
        every { userRepository.get("creator_123") } returns
            testHelper.createUser(stripeAccountId = "acct_123", companyCountry = "CZ")
        every { userRepository.get("user_123") } returns
            testHelper.createUser(companyCountry = "CZ", customerIds = mutableMapOf("EUR" to "cus_123"))

        service.storeCharge(
            appleReferenceId = appleReferenceId,
            appleTransactionId = appleTransactionId,
            timestamp = timestamp,
            tierId = tier.id,
            creatorId = "creator_123",
            userId = "user_123",
            currencyStore = Currency.CZK,
            priceStoreCents = ApplePricingService().computeApplePriceCents(Currency.CZK, tier).toLong(),
            description = "Subscription creation",
            storefront = "CZE",
        )

        assertEquals(199_00, slot.captured.priceStoreCents)
        assertEquals(73_10, slot.captured.priceFeeHeroheroCents)
        assertEquals(25_18, slot.captured.conversionRateCents)
        // apple fee 30% + VAT 21% must be higher than our herohero fee on top of tier
        val minimumPrice = slot.captured.conversionRateCents!! * tier.priceCents * 1.30 * 1.21 / 100
        assertTrue(minimumPrice < slot.captured.priceStoreCents)
        assertEquals(timestamp, slot.captured.createdAt)
        assertEquals(null, slot.captured.transferredAt)
        assertEquals(440, slot.captured.transferCents)
    }

    @Test
    fun `stored apple charge in EUR correctly reflects hero subscription`() {
        val appleReferenceId = UUID.randomUUID().toString()
        val appleTransactionId = UUID.randomUUID().toString()
        val timestamp = instantOf("2025-01-01T00:00:00Z")

        val slot = slot<AppleCharge>()
        every { service.storeCharge(capture(slot)) } just runs
        every { userRepository.get("creator_123") } returns
            testHelper.createUser(stripeAccountId = "acct_123", companyCountry = "SK")
        every { userRepository.get("user_123") } returns
            testHelper.createUser(companyCountry = "CZ", customerIds = mutableMapOf("EUR" to "cus_123"))

        service.storeCharge(
            appleReferenceId = appleReferenceId,
            appleTransactionId = appleTransactionId,
            timestamp = timestamp,
            tierId = tier.id,
            creatorId = "creator_123",
            userId = "user_123",
            currencyStore = Currency.EUR,
            priceStoreCents = pricingService.computeApplePriceCents(Currency.EUR, tier).toLong(),
            description = "Subscription creation",
            storefront = "CZE",
        )

        assertEquals(7_99, slot.captured.priceStoreCents)
        assertEquals(2_99, slot.captured.priceFeeHeroheroCents)
        // apple fee 30% + VAT 21% must be higher than our herohero fee on top of tier
        val minimumPrice = slot.captured.conversionRateCents!! * tier.priceCents * 1.30 * 1.21 / 100
        assertTrue(minimumPrice < slot.captured.priceStoreCents)
        assertEquals(1_00, slot.captured.conversionRateCents)
        assertEquals(timestamp, slot.captured.createdAt)
        assertEquals(null, slot.captured.transferredAt)
        assertEquals(450, slot.captured.transferCents)
    }
}
