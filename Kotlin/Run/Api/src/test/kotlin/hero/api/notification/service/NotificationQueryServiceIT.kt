package hero.api.notification.service

import hero.api.notification.service.NotificationTypeCategory.COMMENT
import hero.api.notification.service.NotificationTypeCategory.COMMUNITY
import hero.baseutils.minus
import hero.core.data.PageRequest
import hero.model.NotificationType
import hero.test.IntegrationTest
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class NotificationQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetNotifications {
        @Test
        fun `should return user's notifications sorted by created at desc`() {
            val underTest = NotificationQueryService(
                TestRepositories.notificationRepository,
                TestRepositories.userRepository,
            )

            val now = Instant.now()
            testHelper.createUser("pavel")
            val actor1 = testHelper.createUser("cestmir")
            val notification1 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now,
                id = "2",
            )
            val notification2 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now - 5.seconds,
                id = "3",
            )
            val notification3 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now - 10.seconds,
                id = "1",
            )

            val result = underTest.execute(GetNotifications("pavel", PageRequest()))

            assertThat(result.hasNext).isFalse()
            assertThat(result.content).containsExactly(
                NotificationWithData(notification1, actor1),
                NotificationWithData(notification2, actor1),
                NotificationWithData(notification3, actor1),
            )
        }

        @Test
        fun `should correctly page through user's notification forward and backwards sorted by created at desc`() {
            val underTest = NotificationQueryService(
                TestRepositories.notificationRepository,
                TestRepositories.userRepository,
            )

            val now = Instant.now()
            val actor1 = testHelper.createUser("cestmir")
            testHelper.createUser("pavel")
            val notification1 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now,
                id = "2",
            )
            val notification2 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now - 5.seconds,
                id = "3",
            )
            val notification3 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now - 10.seconds,
                id = "1",
            )

            val afterPageable1 = PageRequest(pageSize = 1)
            val afterPage1 = underTest.execute(GetNotifications("pavel", afterPageable1))

            // going through notifications from the newest to the oldest
            assertThat(afterPage1.hasNext).isTrue()
            assertThat(afterPage1.content).containsExactly(NotificationWithData(notification1, actor1))

            val afterPageable2 = PageRequest(pageSize = 1, afterCursor = afterPage1.nextPageable.afterCursor)
            val afterPage2 = underTest.execute(GetNotifications("pavel", afterPageable2))

            assertThat(afterPage2.hasNext).isTrue()
            assertThat(afterPage2.content).containsExactly(NotificationWithData(notification2, actor1))

            val afterPageable3 = PageRequest(pageSize = 1, afterCursor = afterPage2.nextPageable.afterCursor)
            val afterPage3 = underTest.execute(GetNotifications("pavel", afterPageable3))

            assertThat(afterPage3.hasNext).isFalse()
            assertThat(afterPage3.content).containsExactly(NotificationWithData(notification3, actor1))

            // we arrived at the oldest, we should be able to fetch newer notifications than notification3
            val beforePageable1 = PageRequest(pageSize = 2, beforeCursor = afterPage3.nextPageable.beforeCursor)
            val beforePage1 = underTest.execute(GetNotifications("pavel", beforePageable1))

            assertThat(beforePage1.hasNext).isFalse()
            assertThat(beforePage1.content).containsExactly(
                NotificationWithData(notification1, actor1),
                NotificationWithData(notification2, actor1),
            )
        }

        @Test
        fun `should fetch only user's notifications and not other users`() {
            val underTest = NotificationQueryService(
                TestRepositories.notificationRepository,
                TestRepositories.userRepository,
            )

            val actor1 = testHelper.createUser("cestmir")
            testHelper.createUser("pavel")
            testHelper.createUser("franta")
            val notification1 = testHelper.createNotification("pavel", actorIds = listOf("cestmir"))
            testHelper.createNotification("franta", actorIds = listOf("cestmir"))

            val result = underTest.execute(GetNotifications("pavel", PageRequest()))

            assertThat(result.content).containsExactly(NotificationWithData(notification1, actor1))
        }

        @Test
        fun `should return notifications only in comment category sorted by created at desc`() {
            val underTest = NotificationQueryService(
                TestRepositories.notificationRepository,
                TestRepositories.userRepository,
            )

            val now = Instant.now()
            testHelper.createUser("pavel")
            val actor1 = testHelper.createUser("cestmir")
            val notification1 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                type = NotificationType.NEW_COMMENT,
                createdAt = now,
            )
            val notification2 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                type = NotificationType.NEW_REPLY,
                createdAt = now - 5.seconds,
            )
            val notification3 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                type = NotificationType.NEW_POST,
                createdAt = now - 10.seconds,
            )

            val result = underTest.execute(GetNotifications("pavel", PageRequest(), setOf(COMMENT)))

            assertThat(result.hasNext).isFalse()
            assertThat(result.content)
                .containsExactly(
                    NotificationWithData(notification1, actor1),
                    NotificationWithData(notification2, actor1),
                )
                .doesNotContain(
                    NotificationWithData(notification3, actor1),
                )
        }

        @Test
        fun `should return community notifications`() {
            val underTest = NotificationQueryService(
                TestRepositories.notificationRepository,
                TestRepositories.userRepository,
            )

            testHelper.createUser("pavel")
            val actor1 = testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            val now = Instant.now()
            val notification1 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                communityId = community.id.toString(),
                type = NotificationType.NEW_THREAD,
                id = "1",
            )
            val notification2 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                createdAt = now,
                id = "2",
            )
            val notification3 = testHelper.createNotification(
                "pavel",
                actorIds = listOf("cestmir"),
                communityId = community.id.toString(),
                type = NotificationType.NEW_COMMENT,
                createdAt = now,
                id = "3",
            )

            val result = underTest.execute(GetNotifications("pavel", PageRequest(), setOf(COMMUNITY)))

            assertThat(result.hasNext).isFalse()
            assertThat(result.content)
                .containsExactly(
                    NotificationWithData(notification1, actor1),
                    NotificationWithData(notification3, actor1),
                )
                .doesNotContain(
                    NotificationWithData(notification2, actor1),
                )
        }
    }
}
