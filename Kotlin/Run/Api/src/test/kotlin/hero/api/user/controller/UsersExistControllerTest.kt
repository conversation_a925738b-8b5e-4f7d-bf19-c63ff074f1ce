package hero.api.user.controller

import hero.api.user.repository.UsersRepository
import hero.baseutils.SystemEnv
import hero.exceptions.http.NotFoundException
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.jackson.jackson
import hero.model.User
import io.mockk.mockk
import io.mockk.spyk
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.http4k.length
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.Base64
import kotlin.test.assertEquals

class UsersExistControllerTest {
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, false)

    private val collection: TypedCollectionReference<User> = firestore.typedCollectionOf(User)

    private val userRepository: UsersRepository = spyk(
        UsersRepository(
            firestoreFulltext = mockk(),
            imageRepository = mockk(),
            pathCollection = mockk(),
            pubSub = mockk(),
            subscriberCollection = mockk(),
            collection = collection,
            accountService = mockk(),
            userRepository = mockk(),
        ),
    )

    private val controller = UsersJsonApiController(
        categoriesRepository = mockk(),
        repository = userRepository,
        pubSub = mockk(),
        tierRepository = mockk(),
        rssFeedsRepository = mockk(),
        deleteUserCommandService = mockk(),
    )

    @Test
    fun `user info-herohero-co must exist`() {
        val request = Request(Method.POST, "/v1/challenge")
            .body(
                jackson.writeValueAsString(
                    UsersJsonApiController.ChallengeBody(
                        Base64.getEncoder().encodeToString("<EMAIL>".toByteArray()),
                    ),
                ),
            )

        val response = controller.routeUserExists(request)
        assertEquals(Status.NO_CONTENT, response.status)
        assertEquals(0, response.body.payload.length())
    }

    @Test
    fun `user info-herohero-not must not exist`() {
        val request = Request(Method.POST, "/v1/challenge")
            .body(
                jackson.writeValueAsString(
                    UsersJsonApiController.ChallengeBody(
                        Base64.getEncoder().encodeToString("<EMAIL>".toByteArray()),
                    ),
                ),
            )

        val exception = assertThrows<NotFoundException> {
            controller.routeUserExists(request)
        }
        assertEquals("Not found.", exception.message)
    }

    @Test
    fun `invalid request results in 404`() {
        val request = Request(Method.POST, "/v1/challenge")
            .body(
                jackson.writeValueAsString(UsersJsonApiController.ChallengeBody("adsf1234")),
            )

        val exception = assertThrows<NotFoundException> {
            controller.routeUserExists(request)
        }
        assertEquals("Not found.", exception.message)
    }
}
