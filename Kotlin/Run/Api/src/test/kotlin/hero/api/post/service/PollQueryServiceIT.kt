package hero.api.post.service

import hero.baseutils.truncated
import hero.exceptions.http.ForbiddenException
import hero.model.Poll
import hero.model.PollOption
import hero.test.IntegrationTest
import hero.test.createVote
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class PollQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetPoll {
        @Test
        fun `should get the poll if the user is a subscriber`() {
            val underTest = PollQueryService(lazyTestContext)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = mapOf("option-id" to PollOption(id = "option-id", title = "option 1", voteCount = 10, 0)),
            )

            testHelper.createUser("cestmir")
            testHelper.createUser("filip")
            testHelper.createSubscription(creatorId = "cestmir", userId = "filip")

            testHelper.createPost("cestmir", poll = poll)

            val result = underTest.execute(GetPoll("filip", "poll-id"))

            assertThat(result.poll).isEqualTo(poll)
        }

        @Test
        fun `should get the poll if author has post previews enabled and post has preview enabled and no user auth`() {
            val underTest = PollQueryService(lazyTestContext)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = mapOf("option-id" to PollOption(id = "option-id", title = "option 1", voteCount = 10, 0)),
            )

            testHelper.createUser("cestmir", hasPostPreviews = true)

            testHelper.createPost("cestmir", poll = poll, hasPreview = true)

            val result = underTest.execute(GetPoll(null, "poll-id"))

            assertThat(result.poll).isEqualTo(poll)
        }

        @Test
        fun `should get the poll if the user is the owner`() {
            val underTest = PollQueryService(lazyTestContext)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = mapOf("option-id" to PollOption(id = "option-id", title = "option 1", voteCount = 10, 0)),
            )

            // no subscriber
            testHelper.createUser("cestmir")
            testHelper.createPost("cestmir", poll = poll)

            val result = underTest.execute(GetPoll("cestmir", "poll-id"))

            assertThat(result.poll).isEqualTo(poll)
        }

        @Test
        fun `should return the poll with user's votes`() {
            val underTest = PollQueryService(lazyTestContext)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = mapOf(
                    "option-id1" to PollOption(id = "option-id1", title = "option 1", voteCount = 10, index = 0),
                    "option-id2" to PollOption(id = "option-id2", title = "option 2", voteCount = 10, index = 1),
                ),
            )

            testHelper.createUser("cestmir")
            testHelper.createUser("filip")
            testHelper.createSubscription(creatorId = "cestmir", userId = "filip")
            testHelper.createUser("pavel")
            testHelper.createPost("cestmir", poll = poll)

            testHelper.createVote("filip", "option-id1")
            // vote of other user for different option
            testHelper.createVote("pavel", "option-id2")

            val result = underTest.execute(GetPoll("filip", "poll-id"))

            assertThat(result).isEqualTo(PollWithVotes(poll, listOf("option-id1")))
        }

        @Test
        fun `should throw forbidden if the user does not subscribe the creator and previews are not allowed`() {
            val underTest = PollQueryService(lazyTestContext)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = mapOf("option-id" to PollOption(id = "option-id", title = "option 1", voteCount = 10, 0)),
            )

            testHelper.createUser("cestmir")
            // missing subscription
            testHelper.createUser("filip")

            testHelper.createPost("cestmir", poll = poll)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetPoll("filip", "poll-id"))
            }
        }
    }
}
