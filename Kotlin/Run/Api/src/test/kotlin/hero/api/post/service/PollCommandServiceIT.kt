package hero.api.post.service

import hero.baseutils.minusDays
import hero.baseutils.plusDays
import hero.baseutils.truncated
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.model.Poll
import hero.model.PollOption
import hero.sql.jooq.Tables.POLL_OPTION_VOTE
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.jooq.DSLContext
import org.junit.jupiter.api.Test
import java.time.Instant

class PollCommandServiceIT : IntegrationTest() {
    @Test
    fun `should cast new votes in a poll and update poll option vote count`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val pollOption1 = PollOption(id = "option-id-1", title = "option 1", voteCount = 1, index = 0)
        val pollOption2 = PollOption(id = "option-id-2", title = "option 2", voteCount = 2, index = 1)
        val pollOption3 = PollOption(id = "option-id-3", title = "option 3", voteCount = 3, index = 2)
        val options = mapOf(
            "option-id-1" to pollOption1,
            "option-id-2" to pollOption2,
            "option-id-3" to pollOption3,
        )
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1).truncated(), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)

        underTest.execute(CastVotesCommand("cestmir", post.id, listOf(Vote("option-id-1"), Vote("option-id-2"))))

        val updatedPost = TestCollections.postsCollection[post.id].get()
        assertThat(updatedPost.poll).isEqualTo(
            poll.copy(
                options = mapOf(
                    "option-id-1" to pollOption1.copy(voteCount = pollOption1.voteCount + 1),
                    "option-id-2" to pollOption2.copy(voteCount = pollOption2.voteCount + 1),
                    "option-id-3" to pollOption3,
                ),
            ),
        )
        assertThat(testContext.selectFrom(POLL_OPTION_VOTE).fetch()).hasSize(2)
    }

    @Test
    fun `should cast add new vote and remove one vot in a poll and update poll option vote count`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val pollOption1 = PollOption(id = "option-id-1", title = "option 1", voteCount = 1, index = 0)
        val pollOption2 = PollOption(id = "option-id-2", title = "option 2", voteCount = 2, index = 1)
        val pollOption3 = PollOption(id = "option-id-3", title = "option 3", voteCount = 3, index = 2)
        val options = mapOf(
            "option-id-1" to pollOption1,
            "option-id-2" to pollOption2,
            "option-id-3" to pollOption3,
        )
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1).truncated(), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)
        testContext.createVote("cestmir", pollOption2.id)

        underTest.execute(CastVotesCommand("cestmir", post.id, listOf(Vote("option-id-1"))))

        val updatedPost = TestCollections.postsCollection[post.id].get()
        assertThat(updatedPost.poll).isEqualTo(
            poll.copy(
                options = mapOf(
                    "option-id-1" to pollOption1.copy(voteCount = pollOption1.voteCount + 1),
                    "option-id-2" to pollOption2.copy(voteCount = pollOption2.voteCount - 1),
                    "option-id-3" to pollOption3,
                ),
            ),
        )
        assertThat(testContext.selectFrom(POLL_OPTION_VOTE).fetch()).hasSize(1)
    }

    @Test
    fun `should remove all votes and update poll option vote count`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val pollOption1 = PollOption(id = "option-id-1", title = "option 1", voteCount = 1, index = 0)
        val pollOption2 = PollOption(id = "option-id-2", title = "option 2", voteCount = 2, index = 1)
        val pollOption3 = PollOption(id = "option-id-3", title = "option 3", voteCount = 3, index = 2)
        val options = mapOf(
            "option-id-1" to pollOption1,
            "option-id-2" to pollOption2,
            "option-id-3" to pollOption3,
        )
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1).truncated(), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)
        testContext.createVote("cestmir", pollOption2.id)
        testContext.createVote("cestmir", pollOption1.id)

        underTest.execute(CastVotesCommand("cestmir", post.id, listOf()))

        val updatedPost = TestCollections.postsCollection[post.id].get()
        assertThat(updatedPost.poll).isEqualTo(
            poll.copy(
                options = mapOf(
                    "option-id-1" to pollOption1.copy(voteCount = pollOption1.voteCount - 1),
                    "option-id-2" to pollOption2.copy(voteCount = pollOption2.voteCount - 1),
                    "option-id-3" to pollOption3,
                ),
            ),
        )
        assertThat(testContext.selectFrom(POLL_OPTION_VOTE).fetch()).isEmpty()
    }

    @Test
    fun `only subscribers can cast votes`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val options = mapOf("option-id-1" to PollOption(id = "option-id-1", title = "option 1", voteCount = 1, 0))
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)

        assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
            underTest.execute(CastVotesCommand("pablo", post.id, listOf(Vote("option-id-1"))))
        }
    }

    @Test
    fun `votes cannot be cast after deadline`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val options = mapOf("option-id-1" to PollOption(id = "option-id-1", title = "option 1", voteCount = 1, 0))
        val poll = Poll(id = "poll-id", deadline = Instant.now().minusDays(1), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)

        assertThatExceptionOfType(BadRequestException::class.java)
            .isThrownBy {
                underTest.execute(CastVotesCommand("cestmir", post.id, listOf(Vote("option-id-1"))))
            }
            .withMessage("Poll poll-id is closed")
    }

    @Test
    fun `can vote with only options from the poll`() {
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection)
        val options = mapOf("option-id-1" to PollOption(id = "option-id-1", title = "option 1", voteCount = 1, 0))
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)

        assertThatExceptionOfType(BadRequestException::class.java)
            .isThrownBy {
                underTest.execute(CastVotesCommand("cestmir", post.id, listOf(Vote("non-existent-option"))))
            }
            .withMessage("Invalid poll option ids were passed [non-existent-option]")
    }

    @Test
    fun `should end the poll now`() {
        val now = Instant.ofEpochSecond(1754409301)
        val testClock = TestClock(now)
        val underTest = PollCommandService(lazyTestContext, TestCollections.postsCollection, testClock)
        val options = mapOf("option-id-1" to PollOption(id = "option-id-1", title = "option 1", voteCount = 1, 0))
        val poll = Poll(id = "poll-id", deadline = Instant.now().plusDays(1), options = options)
        val post = testHelper.createPost("cestmir", poll = poll)

        underTest.execute(EndPoll("cestmir", post.id))

        val updatedPost = TestCollections.postsCollection[post.id].get()
        assertThat(updatedPost.poll).isEqualTo(poll.copy(deadline = now))
    }
}

private fun DSLContext.createVote(
    userId: String,
    pollOptionId: String,
) {
    insertInto(POLL_OPTION_VOTE)
        .set(POLL_OPTION_VOTE.POLL_OPTION_ID, pollOptionId)
        .set(POLL_OPTION_VOTE.USER_ID, userId)
        .execute()
}
