package hero.api.messages.controller

import hero.api.messages.service.GetMessage
import hero.api.messages.service.GetMessagesFromThread
import hero.api.messages.service.MessageQueryService
import hero.api.messages.service.MessageWithPayment
import hero.api.post
import hero.contract.api.dto.PagedPostResponse
import hero.contract.api.dto.PostResponse
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.http4k.auth.jwtFor
import hero.http4k.auth.withAccessTokenCookie
import hero.http4k.auth.withImpersonateTokenCookie
import hero.jackson.fromJson
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class MessageThreadsControllerTest {
    private val messageQueryService = mockk<MessageQueryService>()

    private val underTest = MessageThreadsController(
        messageThreadQueryService = mockk(),
        messageQueryService = messageQueryService,
        messageCommandService = mockk(),
    )

    @Test
    fun `list of dms is anonymized`() {
        val post = post("cestmir", id = "ahoj", text = "ahoj ahoj ahoj", messageThreadId = "dm-thread")
        val content = MessageWithPayment(post, null)

        every {
            messageQueryService.execute(
                GetMessagesFromThread(
                    post.userId,
                    post.messageThreadId!!,
                    PageRequest(0, 20, null, null),
                ),
            )
        } returns Page(listOf(content), PageRequest(), false)

        val responseFull = underTest.routeGetMessages(
            Request(Method.GET, "/v2/message-threads/${post.messageThreadId}/messages")
                .withAccessTokenCookie(jwtFor(post.userId)),
        )

        assertThat(responseFull.status).isEqualTo(Status.OK)
        val bodyFull = responseFull.bodyString().fromJson<PagedPostResponse>()
        assertEquals(1, bodyFull.content.size)
        assertEquals("ahoj ahoj ahoj", bodyFull.content.first().text)

        val responseAnonymized = underTest.routeGetMessages(
            Request(Method.GET, "/v2/message-threads/${post.messageThreadId}/messages")
                .withImpersonateTokenCookie(jwtFor(post.userId)),
        )

        assertThat(responseAnonymized.status).isEqualTo(Status.OK)
        val bodyAnonymized = responseAnonymized.bodyString().fromJson<PagedPostResponse>()
        assertEquals(1, bodyAnonymized.content.size)
        assertEquals("nam legimus salutatus", bodyAnonymized.content.first().text)
    }

    @Test
    fun `get dm is anonymized`() {
        val post = post("cestmir", id = "ahoj", text = "cau cau cau", messageThreadId = "dm-thread")

        every {
            messageQueryService.execute(
                GetMessage(
                    post.userId,
                    post.id,
                ),
            )
        } returns MessageWithPayment(post, null)

        val responseFull = underTest.routeGetMessage(
            Request(Method.GET, "/v1/messages/${post.id}")
                .withAccessTokenCookie(jwtFor(post.userId)),
        )

        assertThat(responseFull.status).isEqualTo(Status.OK)
        val bodyFull = responseFull.bodyString().fromJson<PostResponse>()

        assertEquals("cau cau cau", bodyFull.text)

        val responseAnonymized = underTest.routeGetMessage(
            Request(Method.GET, "/v1/messages/${post.id}")
                .withImpersonateTokenCookie(jwtFor(post.userId)),
        )

        assertThat(responseAnonymized.status).isEqualTo(Status.OK)
        val bodyAnonymized = responseAnonymized.bodyString().fromJson<PostResponse>()
        assertEquals("reprehendunt laoreet reque", bodyAnonymized.text)
    }
}
