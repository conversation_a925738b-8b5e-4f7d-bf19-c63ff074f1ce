package hero.api.community.controller.dto

import hero.api.community.service.CommunityWithMeta
import hero.api.user.controller.dto.toResponse
import hero.api.user.repository.pathUpdateableAfter
import hero.model.Community
import hero.model.User
import java.time.Instant

fun Community.toResponse(
    user: User,
    isMember: <PERSON><PERSON><PERSON>,
) = CommunityResponse(
    id = id,
    name = name,
    description = description,
    slug = slug,
    ownerId = ownerId,
    membersCount = membersCount,
    image = image,
    createdAt = createdAt,
    isVerified = user.creator.verified,
    owner = user.toResponse(listOf()),
    isMember = isMember,
    threadsCount = threadsCount,
    slugEditableAfter = slugUpdatedAt?.pathUpdateableAfter() ?: Instant.EPOCH,
    type = type,
)

fun CommunityWithMeta.toResponse() = this.community.toResponse(this.owner, this.isMember)
