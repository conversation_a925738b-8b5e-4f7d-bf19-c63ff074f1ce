package hero.api.post.service

import hero.baseutils.log
import hero.baseutils.md5nice
import hero.contract.api.dto.ImageAssetInput
import hero.contract.api.dto.PostAssetInput
import hero.exceptions.http.BadRequestException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.gjirafa.dto.toDomainStatus
import hero.model.Chapter
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.ImageAsset
import hero.model.Post
import hero.model.PostAsset
import hero.model.topics.PostState.DELETED
import hero.model.topics.PostState.PROCESSING
import hero.model.topics.PostState.PUBLISHED
import hero.model.topics.PostState.REVISION
import hero.model.topics.PostState.SCHEDULED
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.repository.post.PostRepository
import java.time.Clock
import java.time.Instant
import java.util.UUID

class PostService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val gjirafaService: GjirafaUploadsService,
    private val gjirafaLivestreamService: GjirafaLivestreamsService,
    private val pubSub: PubSub,
    private val clock: Clock = Clock.systemUTC(),
) {
    /**
     * This factory method creates generic Post entity. This function should be called only from use cases, such as
     * [CreateComment], etc. This method does only basic validations (such as either text or assets are set),
     * specific validations should be done for given use case.
     */
    fun execute(command: CreatePost): Post {
        validateTextAndInput(command.text, command.textHtml, command.assets, command.userId)
        val (assets, assetsReady) = processAssets(
            command.assets,
            command.userId,
            emptyMap(),
            command.validateGjirafaAuthor,
        )
        val state = when {
            !assetsReady -> PROCESSING
            command.publishedAt.isAfter(Instant.now()) -> SCHEDULED
            else -> PUBLISHED
        }

        val id = generateId(
            messageThreadId = command.messageThreadId,
            parentId = command.parentId,
            text = command.text,
            userId = command.userId,
        )

        if (assets.any { it.isEmpty() }) {
            log.fatal("One of assets was empty for post $id: $assets", mapOf("userId" to command.userId))
        }

        val now = Instant.now(clock)
        val post = Post(
            id = id,
            userId = command.userId,
            parentId = command.parentId,
            siblingId = command.siblingId,
            messageThreadId = command.messageThreadId,
            communityId = command.communityId?.toString(),
            parentUserId = command.parentUserId,
            parentPostId = command.parentPostId,
            published = command.publishedAt,
            pinnedAt = command.pinnedAt,
            updated = now,
            isSponsored = command.isSponsored,
            isAgeRestricted = command.isAgeRestricted,
            created = now,
            state = state,
            title = command.title?.trim(),
            text = command.text.trim(),
            textHtml = command.textHtml?.let { htmlSanitizer.sanitize(it).trim() },
            textDelta = command.textDelta?.trim(),
            excludeFromRss = command.excludeFromRss,
            assets = assets,
            assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
            assetStates = assets.mapNotNull { it.gjirafa?.status }.distinct(),
            price = command.price,
            categories = command.categories.toList(),
            participingUserIds = command.participatingUserIds,
            chapters = command.chapters,
            hasPreview = command.hasPreview,
        ).also {
            postsCollection[it.id].set(it)
            postRepository.save(it)
        }

        if (post.state in publishableStates) {
            pubSub.publish(PostStateChanged(PostStateChange.PUBLISHED, post))
        }

        return post
    }

    fun execute(command: UpdatePost): Post {
        val post = postsCollection[command.postId].get().apply {
            validateTextAndInput(command.text, command.textHtml, command.assets, command.userId)
            command.postValidator(this)
            validateState(this, command.userId)
        }

        val publishedAt = command.publishedAt ?: post.published
        val blurredUrls = post.assets
            .flatMap { asset ->
                listOfNotNull(
                    asset.image?.let { it.id to it.blurredUrl },
                    asset.thumbnail?.let { it to asset.thumbnailBlurUrl },
                    asset.thumbnailImage?.let { it.id to it.blurredUrl },
                )
            }
            .toMap()

        val (assets, assetsReady) = processAssets(command.assets, command.userId, blurredUrls, true)
        val state = when {
            // we currently never unpublish posts, might want to change in the future
            post.state == PUBLISHED -> PUBLISHED
            !assetsReady -> PROCESSING
            publishedAt.isAfter(Instant.now()) && post.state == PUBLISHED && assetsReady -> SCHEDULED
            else -> post.state
        }

        val updatedPost = post.copy(
            updated = Instant.now(),
            title = command.title?.trim(),
            text = command.text.trim(),
            textHtml = htmlSanitizer.sanitize(command.textHtml).trim(),
            textDelta = command.textDelta?.trim(),
            published = publishedAt,
            state = state,
            price = command.price,
            pinnedAt = command.pinnedAt,
            categories = command.categories,
            excludeFromRss = command.excludeFromRss ?: post.excludeFromRss,
            assets = assets,
            assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
            assetStates = assets.mapNotNull { it.gjirafa?.status }.distinct(),
            chapters = command.chapters,
            isSponsored = command.isSponsored,
            isAgeRestricted = command.isAgeRestricted,
            hasPreview = command.hasPreview,
        )

        postsCollection[post.id].set(updatedPost)
        postRepository.save(updatedPost)
        if (post.state in publishableStates) {
            pubSub.publish(PostStateChanged(PostStateChange.PATCHED, updatedPost))
        }

        return updatedPost
    }

    private fun processAssets(
        assets: List<PostAssetInput>,
        creatorId: String,
        blurredUrls: Map<String, String?>,
        validateGjirafaAuthor: Boolean,
    ): Pair<List<PostAsset>, Boolean> {
        val mappedAssets = assets.map { processAsset(it, creatorId, blurredUrls, validateGjirafaAuthor) }
        val readyStates = setOf(COMPLETE, PARTIALLY_COMPLETED)
        val gjirafaReady = mappedAssets
            .mapNotNull { it.gjirafa }
            .all { it.status in readyStates }

        return mappedAssets to gjirafaReady
    }

    private fun validateState(
        post: Post,
        userId: String,
    ) {
        val labels = mapOf("userId" to userId, "postId" to post.id)
        if (post.state == DELETED || post.state == REVISION) {
            throw BadRequestException("Cannot patch deleted posts", labels)
        }
    }

    private fun validateTextAndInput(
        text: String,
        textHtml: String?,
        assets: List<PostAssetInput>,
        userId: String,
    ) {
        val labels = mapOf("userId" to userId)
        val textFieldsAreBlank = text.isBlank() && textHtml.isNullOrBlank()
        if (textFieldsAreBlank && assets.isEmpty()) {
            throw BadRequestException("Either text or some assets must be given to a post", labels)
        }
    }

    private fun processAsset(
        assetDto: PostAssetInput,
        creatorId: String,
        blurredUrls: Map<String, String?>,
        validateGjirafaAuthor: Boolean,
    ): PostAsset =
        PostAsset(
            image = assetDto.image?.toImageAsset()?.let { it.copy(blurredUrl = blurredUrls[it.id]) },
            gjirafa = assetDto.gjirafa?.let {
                gjirafaService.getAsset(userId = if (validateGjirafaAuthor) creatorId else null, assetId = it.id)
            },
            thumbnail = assetDto.thumbnail,
            thumbnailBlurUrl = blurredUrls[assetDto.thumbnail],
            thumbnailImage = assetDto.thumbnailImage?.toImageAsset()?.let { it.copy(blurredUrl = blurredUrls[it.id]) },
            document = assetDto.document,
            gjirafaLive = assetDto.gjirafaLivestream?.let {
                val response = gjirafaLivestreamService.getLiveVideo(it.id)

                GjirafaLiveAsset(
                    id = it.id,
                    playbackUrl = response.playbackUrl,
                    channelPublicId = response.channelPublicId,
                    liveStatus = response.liveStatus.toDomainStatus(),
                )
            },
            youTube = assetDto.youtube,
        )

    private fun ImageAssetInput.toImageAsset() =
        ImageAsset.of(id = url, width = width, height = height, fileName = fileName, fileSize = fileSize)

    private fun generateId(
        messageThreadId: String?,
        parentId: String?,
        text: String,
        userId: String,
    ): String {
        val currentTimeMillis = System.currentTimeMillis()
        return if (messageThreadId != null) {
            "$messageThreadId-$currentTimeMillis-${(userId + text).md5nice()}"
        } else if (parentId != null) {
            "$parentId-${(userId + currentTimeMillis).md5nice().take(HASH_LENGTH)}"
        } else {
            userId + (text + userId + currentTimeMillis).md5nice()
        }
    }
}

data class CreatePost(
    val userId: String,
    val parentId: String? = null,
    val siblingId: String? = null,
    val parentUserId: String? = null,
    val parentPostId: String? = null,
    val messageThreadId: String? = null,
    val communityId: UUID? = null,
    val publishedAt: Instant,
    val pinnedAt: Instant? = null,
    val title: String? = null,
    val text: String,
    val textHtml: String?,
    val textDelta: String?,
    val isSponsored: Boolean,
    val isAgeRestricted: Boolean,
    val assets: List<PostAssetInput>,
    val price: Long? = null,
    val excludeFromRss: Boolean = false,
    val categories: Set<String> = setOf(),
    val participatingUserIds: List<String> = emptyList(),
    val chapters: List<Chapter> = emptyList(),
    val hasPreview: Boolean = true,
    val validateGjirafaAuthor: Boolean = true,
)

data class UpdatePost(
    val postId: String,
    val userId: String,
    val publishedAt: Instant? = null,
    val pinnedAt: Instant? = null,
    val title: String? = null,
    val text: String,
    val textHtml: String?,
    val textDelta: String?,
    val isSponsored: Boolean,
    val isAgeRestricted: Boolean,
    val assets: List<PostAssetInput>,
    val price: Long? = null,
    val excludeFromRss: Boolean? = false,
    val categories: List<String> = emptyList(),
    val chapters: List<Chapter> = emptyList(),
    val postValidator: (Post) -> Unit = {},
    val hasPreview: Boolean = true,
)

private const val HASH_LENGTH = 8

private val publishableStates = setOf(PUBLISHED)
