package hero.api.community.service

import hero.api.user.repository.pathUpdateableAfter
import hero.baseutils.log
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.model.Community
import hero.model.CommunityMemberStatus
import hero.model.CommunityType
import hero.model.ImageAsset
import hero.model.topics.CommunityCreated
import hero.model.topics.CommunityUpdated
import hero.repository.community.CommunityRepository
import hero.repository.community.addCommunityMember
import hero.repository.community.removeCommunityMember
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class CommunityCommandService(
    private val communityRepository: CommunityRepository,
    private val userRepository: UserRepository,
    private val pubSub: PubSub,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
    private val logger: Logger = log,
) {
    val context: DSLContext by lazyContext

    fun execute(command: CreateCommunity): Community {
        val creator = userRepository.getById(command.userId)

        if (communityRepository.findByOwnerId(command.userId).isNotEmpty()) {
            throw ConflictException("User ${command.userId} already has a community")
        }

        val now = Instant.now(clock)
        val community = Community(
            id = UUID.randomUUID(),
            name = creator.name,
            description = "Community",
            slug = creator.path,
            ownerId = command.userId,
            membersCount = 1,
            image = creator.image,
            createdAt = now,
            updatedAt = now,
            deletedAt = null,
            threadsCount = 0,
            slugUpdatedAt = null,
            type = CommunityType.CONNECTED,
        )

        communityRepository.save(community)
        pubSub.publish(CommunityCreated(community.id))
        context
            .update(Tables.USER)
            .set(Tables.USER.OWNED_COMMUNITIES_COUNT, Tables.USER.OWNED_COMMUNITIES_COUNT.plus(1))
            .where(Tables.USER.ID.eq(command.userId))
            .execute()

        return community
    }

    fun execute(command: UpdateCommunity): Community {
        validateSlug(command.slug)
        val community = communityRepository.getById(command.communityId)
        val owner = userRepository.getById(community.ownerId)

        if (command.userId != community.ownerId) {
            throw ForbiddenException("User ${command.userId} cannot update community ${command.communityId}")
        }

        val slugUpdatedAt = if (command.slug != community.slug) {
            val communityWithSlug = context
                .selectFrom(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.SLUG.eq(command.slug))
                .fetchOne()

            if (communityWithSlug != null) {
                throw ConflictException("Community with slug ${command.slug} already exists")
            }

            if (command.slug != owner.path) {
                val userWithSlug = context.selectFrom(Tables.USER)
                    .where(Tables.USER.PATH.eq(command.slug))
                    .fetch()

                if (userWithSlug.isNotEmpty) {
                    throw ConflictException("User with slug ${command.slug} already exists")
                }
            }

            if (community.slugUpdatedAt?.pathUpdateableAfter()?.isAfter(Instant.now()) == true) {
                throw ConflictException("Community slug can be changed once in an hour")
            }

            Instant.now(clock)
        } else {
            community.slugUpdatedAt
        }

        val updatedCommunity = community.copy(
            name = command.name,
            description = command.description,
            image = command.image,
            slug = command.slug,
            updatedAt = Instant.now(clock),
            slugUpdatedAt = slugUpdatedAt,
            type = command.type,
        )
        communityRepository.save(updatedCommunity)
        pubSub.publish(CommunityUpdated(oldValue = community, newValue = updatedCommunity))

        return updatedCommunity
    }

    fun execute(command: JoinCommunity) {
        val community = communityRepository.getById(command.communityId)

        if (community.type != CommunityType.FREE) {
            throw BadRequestException("Only free communities can be joined")
        }

        val communityMember = context.selectFrom(Tables.COMMUNITY_MEMBER)
            .where(Tables.COMMUNITY_MEMBER.USER_ID.eq(command.userId))
            .and(Tables.COMMUNITY_MEMBER.COMMUNITY_ID.eq(command.communityId))
            .fetchOne()

        if (communityMember?.state == CommunityMemberStatus.ACTIVE.name) {
            throw ConflictException("User ${command.userId} is already a member of community ${command.communityId}")
        }

        addCommunityMember(context, command.communityId, command.userId, Instant.now(clock), logger)
    }

    fun execute(command: LeaveCommunity) {
        val community = communityRepository.getById(command.communityId)
        val userId = command.userId

        if (community.ownerId == userId) {
            throw BadRequestException("Community owner cannot leave the community")
        }

        if (community.type != CommunityType.FREE) {
            throw BadRequestException("Only free communities can be left")
        }

        val communityMember = context.selectFrom(Tables.COMMUNITY_MEMBER)
            .where(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
            .and(Tables.COMMUNITY_MEMBER.COMMUNITY_ID.eq(command.communityId))
            .fetchOne()

        if (communityMember == null) {
            throw BadRequestException("User $userId is not a member of community ${command.communityId}")
        }

        if (communityMember.state != CommunityMemberStatus.ACTIVE.name) {
            throw BadRequestException("User $userId is not an active member of community ${command.communityId}")
        }

        val now = Instant.now(clock)
        removeCommunityMember(context, command.communityId, userId, CommunityMemberStatus.LEFT, now, logger)
    }

    private fun validateSlug(slug: String) {
        if (slug.length < 3) {
            throw BadRequestException("Slug must be at least 3 characters long")
        }

        if (!slug.matches("[a-z0-9]+".toRegex())) {
            throw BadRequestException("Slug must contain only lowercase letters and numbers")
        }
    }
}

data class LeaveCommunity(val userId: String, val communityId: UUID)

data class JoinCommunity(val userId: String, val communityId: UUID)

data class CreateCommunity(val userId: String)

data class UpdateCommunity(
    val communityId: UUID,
    val userId: String,
    val name: String,
    val description: String,
    val image: ImageAsset?,
    val slug: String,
    val type: CommunityType,
)
