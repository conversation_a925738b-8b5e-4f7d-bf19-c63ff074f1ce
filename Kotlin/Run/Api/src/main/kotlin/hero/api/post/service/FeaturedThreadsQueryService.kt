package hero.api.post.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.core.data.Sort
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.CommunityType
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Post
import hero.model.topics.PostState
import hero.repository.post.JooqPostHelper
import hero.repository.user.JooqUserHelper
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.USER
import hero.sql.tupleSorting
import org.jooq.DSLContext
import java.time.Instant

class FeaturedThreadsQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(query: GetMostUpVotedThreads): Page<Post> {
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetMostUpVotedThreadsCursor>()

        val isPrivateCommunity = COMMUNITY.TYPE.eq(CommunityType.CONNECTED.name)
            .and(JooqUserHelper.tierId.eq(FREE_SUBSCRIBER_TIER_ID))

        val (threads, hasNext) = context
            .select(JooqPostHelper.postFields)
            .from(POST)
            .join(COMMUNITY).on(COMMUNITY.ID.eq(POST.COMMUNITY_ID))
            .join(USER).on(USER.ID.eq(COMMUNITY.OWNER_ID))
            .where(POST.COMMUNITY_ID.isNotNull)
            .and(POST.PUBLISHED_AT.gt(query.since))
            .and(POST.STATE.eq(PostState.PUBLISHED.name))
            .and(isPrivateCommunity.isFalse)
            .let {
                if (afterCursor != null) {
                    it.tupleSorting(
                        POST.VOTE_SCORE,
                        POST.ID,
                        afterCursor.voteScore,
                        afterCursor.id,
                        Sort(direction = Sort.Direction.DESC),
                    )
                } else {
                    it.orderBy(POST.VOTE_SCORE.desc(), POST.ID.desc())
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()
            .map { JooqPostHelper.mapRecordToEntity(it) }
            .let {
                it.take(query.pageable.pageSize) to (it.size > query.pageable.pageSize)
            }

        return Page(threads, nextPageable(threads, query.pageable), hasNext)
    }

    private fun nextPageable(
        threads: List<Post>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = threads
            .lastOrNull()
            ?.let {
                GetMostUpVotedThreadsCursor(it.voteScore, it.id)
            }
            ?.toJson()
            ?.toBase64()

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, sort = pageable.sort)
    }
}

data class GetMostUpVotedThreads(val since: Instant, val pageable: Pageable)

private data class GetMostUpVotedThreadsCursor(val voteScore: Int, val id: String)
