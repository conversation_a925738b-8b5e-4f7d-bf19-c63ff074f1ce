package hero.api.user.service

import hero.baseutils.log
import hero.core.data.Sort
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.isNull
import hero.gcloud.where
import hero.model.Category
import hero.model.Path
import hero.model.User
import hero.model.UserStatus
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables.USER

class UserQueryService(
    private val userRepository: UserRepository,
    private val categoriesCollection: TypedCollectionReference<Category>,
    private val pathsCollection: TypedCollectionReference<Path>,
    private val logger: Logger = log,
) {
    fun execute(query: GetUser): UserWithCategories = fetchUserWithCategories(query.userId)

    fun execute(query: GetUserDetails): UserWithCategories {
        val (user, categories) = fetchUserWithCategories(query.userId)

        return UserWithCategories(user, categories)
    }

    fun execute(query: FindUserByEmail): User? {
        if ("@" !in query.email) {
            throw BadRequestException()
        }
        val users = userRepository.find {
            this
                .where(USER.EMAIL.eq(query.email).and(USER.STATUS.eq(UserStatus.ACTIVE.name)))
        }

        if (users.size > 1) {
            logger.fatal("Found more than 1 active user for email ${query.email}")
        }

        return users.firstOrNull()
    }

    private fun fetchUserWithCategories(userId: String): UserWithCategories {
        val userById = userRepository.findById(userId)

        val user = (userById ?: fetchUserByPath(userId))
            ?.takeIf { it.status != UserStatus.DELETED }
            ?: throw userNotFoundException(userId)

        val categories = categoriesCollection
            .where(Category::userId).isEqualTo(user.id)
            .orderBy(Category::index, Sort.Direction.ASC)
            .fetchAll()

        return UserWithCategories(user, categories)
    }

    private fun fetchUserByPath(path: String): User? =
        pathsCollection
            .where(Path::id).isEqualTo(path.lowercase())
            .and(Path::abandoned).isNull()
            .fetchSingle()
            ?.userId
            ?.let { userRepository.findById(it) }
            ?: userRepository.findSingle { where(USER.PATH.eq(path).and(USER.STATUS.eq(UserStatus.ACTIVE.name))) }

    private fun userNotFoundException(id: String) = NotFoundException("User $id not found")
}

data class GetUser(val userId: String)

data class FindUserByEmail(val email: String)

data class GetUserDetails(val userId: String)

data class UserWithCategories(val user: User, val categories: List<Category>)
