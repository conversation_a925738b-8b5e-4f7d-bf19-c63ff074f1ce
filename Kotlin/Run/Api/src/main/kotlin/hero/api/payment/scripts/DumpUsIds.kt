package hero.api.payment.scripts

import com.google.cloud.firestore.Query
import com.stripe.param.SubscriptionListParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.tryThread
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.User

fun main() {
    val customerIds = mutableSetOf<String>()
    val accountIds = mutableSetOf<String>()
    val subscribptionIds = mutableSetOf<String>()

    val production = false
    val cloudProject = SystemEnv.cloudProject
    val (_, _, _, _, clients) = initializeStripeScript(production)
    val firestoreRef = firestore(cloudProject, production)
    val userCollection = firestoreRef.typedCollectionOf(User)
    var cursor = ""

    while (true) {
        val list = userCollection
            .where(User::id).isNotEqualTo("")
            .orderBy(User::id, Query.Direction.ASCENDING)
            .startAfter(cursor)
            .limit(100)
            .fetchAll()

        cursor = list.lastOrNull()?.id ?: break

        list.forEach {
            try {
                val usCustomer = it.customerIds.get(Currency.USD.name)
                if (usCustomer != null) {
                    customerIds += usCustomer
                    tryThread {
                        subscribptionIds += clients[Currency.EUR].subscriptions()
                            .list(SubscriptionListParams.builder().setCustomer(usCustomer).build())
                            .autoPagingIterable()
                            .map { it.id }
                    }
                }
                if ("USD" in it.creator.tierId && it.creator.stripeAccountId != null) {
                    accountIds += it.creator.stripeAccountId!!
                }
            } catch (e: Exception) {
                log.error("${it.id}/${it.creator.stripeAccountId}: ${e.message}")
            }
        }
    }

    println("customers:")
    println(customerIds)
    println("accounts:")
    println(accountIds)
    println("subs:")
    println(subscribptionIds)
}
