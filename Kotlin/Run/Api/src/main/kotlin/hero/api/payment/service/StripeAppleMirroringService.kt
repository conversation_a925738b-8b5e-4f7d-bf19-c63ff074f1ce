package hero.api.payment.service

import com.stripe.model.Coupon
import com.stripe.param.CouponCreateParams
import hero.api.invoice.util.fetchCzkConversionRate
import hero.api.payment.controller.PaymentResponse
import hero.api.payment.controller.dto.APPLE_SANDBOX_CREATOR_ID
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.exceptions.http.ConflictException
import hero.gcloud.TypedCollectionReference
import hero.model.AppleCharge
import hero.model.CouponMethod
import hero.model.Currency
import hero.model.Subscriber
import hero.model.Tier
import hero.model.User
import hero.model.topics.CardCreateType
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.stripe.service.StripeClients
import hero.stripe.service.VatMapping
import hero.stripe.service.computeFee
import org.jooq.DSLContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.ZoneOffset
import java.util.UUID

class StripeAppleMirroringService(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val clients: StripeClients,
    private val userRepository: UsersRepository,
    private val appleCharges: TypedCollectionReference<AppleCharge>,
    private val countryToVatMapping: VatMapping,
) {
    private val context by lazyContext

    private fun appleCouponFactory(
        userId: String,
        creator: User,
        tier: Tier,
    ): Coupon {
        val price = subscriberStripeRepository.priceFactory(creator, tier)
        return clients[tier.currency].coupons().create(
            CouponCreateParams.builder()
                .setId("APPLE-${tier.currency}-${UUID.randomUUID()}")
                .setDuration(CouponCreateParams.Duration.FOREVER)
                .setAppliesTo(
                    CouponCreateParams.AppliesTo.builder()
                        .addProduct(price.product)
                        .build(),
                )
                .setPercentOff(100.toBigDecimal())
                .setMetadata(
                    mapOf(
                        "purchasedByUserId" to userId,
                        "couponMethod" to CouponMethod.APPLE_IN_APP.name,
                        "creatorId" to creator.id,
                    ),
                )
                .build(),
        )
    }

    fun createSubscription(
        appleReferenceId: String,
        appleTransactionId: String,
        userId: String,
        creatorId: String,
        tierId: String,
        currencyStore: Currency,
        priceStoreCents: Long,
        storefront: String?,
    ): PaymentResponse {
        val tier = Tier.ofId(tierId)
        val user = userRepository.get(userId)
        val creator = userRepository.get(creatorId)

        if (!creator.creator.active) {
            throw ConflictException(
                "Creator ${creator.id} has not finished their Stripe account pairing.",
                mapOf("creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
            )
        }

        if (creator.id == user.id) {
            throw ConflictException(
                "User ${user.id} cannot subscribe themselves.",
                mapOf("userId" to user.id, "appleReferenceId" to appleReferenceId),
            )
        }

        val coupon = appleCouponFactory(userId, creator, tier)
        val subscription = subscriberStripeRepository.subscribe(
            user = user,
            creator = creator,
            paymentMethodId = null,
            couponId = coupon.id,
            cardCreateType = CardCreateType.APPLE_IN_APP,
            metadata = mapOf(
                "Apple Subscription \uF8FF" to "Do not cancel ❌",
                "\uF8FF Apple Subscription" to "Do not cancel ❌",
                Subscriber::appleReferenceId.name to appleReferenceId,
                Subscriber::appleTransactionId.name to appleTransactionId,
                Subscriber::applePriceCents.name to priceStoreCents.toString(),
                Subscriber::appleCurrency.name to currencyStore.name,
            ),
        )

        // transfer must also be made as this is an initial payment
        storeCharge(
            appleReferenceId,
            appleTransactionId,
            Instant.now(),
            tierId,
            creatorId,
            userId,
            currencyStore,
            priceStoreCents,
            "Subscription creation",
            storefront,
        )

        log.info(
            "Replicated Apple subscription $appleReferenceId of ${user.id} -> ${creator.id}" +
                " as ${subscription.attributes.stripeId}/${subscription.attributes.subscriptionStatus}",
            mapOf("userId" to user.id, "creatorId" to creator.id, "appleReferenceId" to appleReferenceId),
        )
        return subscription
    }

    fun appleCancelledAtPeriodEnd(
        appleReferenceId: String,
        cancelAtPeriodEnd: Boolean,
    ) {
        subscriberStripeRepository.patchSubscription(appleReferenceId, cancelAtPeriodEnd)
    }

    fun cancel(
        appleReferenceId: String,
        appleTransactionId: String,
        refund: Boolean,
        cancelAt: Instant,
    ) {
        subscriberStripeRepository.cancelSubscription(appleReferenceId)
        if (refund) {
            appleCharges[appleTransactionId].field(AppleCharge::refundedAt).update(cancelAt)
        }
    }

    fun assertNotExisting(
        userId: String,
        creatorId: String,
    ) {
        val subscriptions = context.selectFrom(SUBSCRIPTION)
            .where(
                SUBSCRIPTION.USER_ID.eq(userId)
                    .and(SUBSCRIPTION.CREATOR_ID.eq(creatorId))
                    .and(JooqSubscriptionHelper.activeSubscription),
            )
            .fetch()

        if (subscriptions.isNotEmpty) {
            throw ConflictException(
                "Subscription for $userId -> $creatorId already exists",
                mapOf("userId" to userId, "creatorId" to creatorId),
            )
        }
    }

    fun storeCharge(
        appleReferenceId: String,
        appleTransactionId: String,
        timestamp: Instant,
        tierId: String,
        creatorId: String,
        userId: String,
        currencyStore: Currency,
        priceStoreCents: Long,
        description: String,
        storefront: String?,
    ) {
        val creator = userRepository.get(creatorId)
        val user = userRepository.get(userId)
        val tier = Tier.ofId(tierId)
        val conversionRateCents = when (currencyStore) {
            Currency.CZK -> fetchCzkConversionRate(tier.currency, timestamp.atZone(ZoneOffset.UTC).toLocalDate())
                .movePointRight(2).toLong()
            // for other currencies, we use estimates, but these don't go to accountancy, so it should not matter
            Currency.USD -> 1_25L
            Currency.PLN -> 4_50L
            Currency.EUR -> 1_00L
            else -> error("Unsupported currency $currencyStore for $appleReferenceId/$appleTransactionId")
        }

        val feeHerohero = priceStoreCents - (tier.priceCents * conversionRateCents / 1_00)

        val (transferPerCents, _, _) = computeFee(
            feePercents = tier.feePercents,
            creatorVatId = creator.company!!.vatId,
            creatorCountry = creator.company!!.country ?: "",
            instant = timestamp,
            countryToVatMapping = countryToVatMapping,
        )
        val transferCents = transferPerCents!!.times(tier.priceCents.toBigDecimal())
            .divide(BigDecimal(100))
            .setScale(0, RoundingMode.HALF_UP)
            .toLong()

        storeCharge(
            AppleCharge(
                appleTransactionId = appleTransactionId,
                appleReferenceId = appleReferenceId,
                createdAt = timestamp,
                refundedAt = null,
                userId = userId,
                creatorId = creatorId,
                tierId = tierId,
                targetAccountId = creator.creator.stripeAccountId!!,
                stripeTransferId = null,
                stripeTransferReversalId = null,
                transferredAt = null,
                transferReversedAt = null,
                currencyStore = currencyStore,
                priceStoreCents = priceStoreCents,
                conversionRateCents = conversionRateCents,
                priceFeeHeroheroCents = feeHerohero,
                transferCents = transferCents,
                customerId = user.customerIds[tier.currency.name] ?: error("Missing customer id for $userId"),
                description = description,
                storefront = storefront,
            ),
        )
    }

    internal fun storeCharge(charge: AppleCharge) {
        if (charge.creatorId == APPLE_SANDBOX_CREATOR_ID) {
            // skipping sandboxed users in production
            return
        }
        appleCharges[charge.appleTransactionId].set(charge)
    }
}
