package hero.api.payment.controller

import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.api.user.repository.toDto
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.model.Currency
import hero.model.TierDto
import hero.model.TierDtoAttributes
import hero.model.TierDtoRelationships
import hero.model.euroCurrencyRelatedCountries
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.string

class TiersController(
    private val tiersRepository: TiersRepository,
    private val usersRepository: UsersRepository,
) {
    @Suppress("unused")
    val routeTiers: ContractRoute =
        "/v2/tiers".get(
            summary = "List tier levels (according to creator's country).",
            tag = "Subscriptions",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example TiersResponseV2(tiersRepository.tiers.map { it.toDto() })),
            meta = { it.markAsDeprecated() },
            handler = { request, _ ->
                val user = usersRepository.get(request)
                val country = user.company?.country?.uppercase()
                    ?: throw BadRequestException(
                        "User ${user.id} must have country assigned to retrieve list of Tiers.",
                    )
                val expectedCurrency = when {
                    country in euroCurrencyRelatedCountries -> Currency.EUR
                    else -> Currency.USD
                }
                val filteredTiers = tiersRepository.getTiers(expectedCurrency)
                    .map { it.toDto() }
                Response(Status.OK)
                    .body(TiersResponseV2(filteredTiers))
            },
        )

    @Suppress("unused")
    val routeGetSingleTier: ContractRoute =
        ("/v2/tiers" / Path.string().of("tierId")).get(
            summary = "Get Tier by tierId",
            tag = "Subscriptions",
            parameters = object {},
            responses = listOf(
                Status.OK example TierDto(
                    "EUR05",
                    attributes = TierDtoAttributes(price = 500, default = true, currency = Currency.EUR),
                    relationships = TierDtoRelationships,
                ),
            ),
            handler = { _, _, tierId ->
                val tier = tiersRepository.tiers.find { it.id == tierId } ?: throw NotFoundException()
                Response(Status.OK)
                    .body(tier.toDto())
            },
        )

    @Deprecated("Use JSON/API version.")
    data class TiersResponseV2(
        val tiers: List<TierDto>,
    )
}
