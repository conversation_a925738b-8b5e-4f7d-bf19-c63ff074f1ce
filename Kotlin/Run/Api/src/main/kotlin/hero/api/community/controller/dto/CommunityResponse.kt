package hero.api.community.controller.dto

import hero.api.user.controller.dto.UserResponse
import hero.core.data.SimplePageResponse
import hero.model.CommunityType
import hero.model.ImageAsset
import java.time.Instant
import java.util.UUID

data class CommunityResponse(
    val id: UUID,
    val name: String,
    val description: String,
    val slug: String,
    val ownerId: String,
    val owner: UserResponse,
    val membersCount: Long,
    val image: ImageAsset?,
    val createdAt: Instant,
    val isVerified: Boolean,
    val isMember: Boolean,
    val threadsCount: Long,
    val slugEditableAfter: Instant,
    val type: CommunityType,
)

data class UpdateCommunityRequest(
    val name: String,
    val description: String,
    val image: ImageAsset?,
    val slug: String,
    val type: CommunityType,
)

data class PagedCommunityResponse(
    override val content: List<CommunityResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<CommunityResponse>
