package hero.api.statistics.service

import hero.baseutils.nullIfEmpty
import hero.gcloud.TypedCollectionReference
import hero.model.CZ_VAT_COUNTRY
import hero.model.CouponMethod
import hero.model.User
import hero.model.europeanUnionCountries
import hero.sql.jooq.tables.Subscription.SUBSCRIPTION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant

class IncomeQueryService(
    private val usersCollection: TypedCollectionReference<User>,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(query: GetExpectedIncome): ExpectedIncome {
        val now = Instant.now()
        val creator = usersCollection[query.creatorId].get()

        val country = creator.company?.country?.nullIfEmpty()?.uppercase() ?: CZ_VAT_COUNTRY
        val vatId = creator.company?.vatId.nullIfEmpty()
        val vatPercent = if ((country == CZ_VAT_COUNTRY || (country in europeanUnionCountries && vatId == null))) {
            // TODO use vatMappingProvider.countryToVatMapping() to get the correct VAT rate for given country
            2.1
        } else {
            0.0
        }
        val baseFeePercents = 10.0
        val feePercents = vatPercent + baseFeePercents

        val percent = DSL.value(1).minus(DSL.coalesce(SUBSCRIPTION.COUPON_PERCENT_OFF, 0).div(100.0))
        val priceCents = SUBSCRIPTION.PRICE_CENTS.times(
            DSL
                .`when`(SUBSCRIPTION.COUPON_METHOD.eq(CouponMethod.APPLE_IN_APP.name), 1)
                .`when`(SUBSCRIPTION.COUPON_EXPIRES_AT.isNull, percent)
                .`when`(DSL.instant(now).gt(SUBSCRIPTION.COUPON_EXPIRES_AT), 1)
                .otherwise(percent),
        )

        val sum = DSL.sum(priceCents).`as`("total_income")
        return context.select(sum)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.CREATOR_ID.eq(query.creatorId))
            .and(SUBSCRIPTION.STATUS.`in`("active", "past_due"))
            .and(SUBSCRIPTION.CANCELLED_AT.isNull)
            .fetchSingle()
            .map {
                val grossIncomeCents = it[sum]?.toInt() ?: 0
                ExpectedIncome(
                    grossIncomeCents = grossIncomeCents,
                    netIncomeCents = (grossIncomeCents - (grossIncomeCents * (feePercents / 100.0))).toInt(),
                    feePercents = feePercents,
                )
            }
    }
}

data class GetExpectedIncome(val creatorId: String)

data class ExpectedIncome(val grossIncomeCents: Int, val netIncomeCents: Int, val feePercents: Double)
