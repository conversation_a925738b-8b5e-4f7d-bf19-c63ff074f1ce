package hero.api.community.controller.dto

import hero.api.user.controller.dto.exampleUserResponse
import hero.model.CommunityType
import hero.model.ImageAsset
import java.time.Instant
import java.util.UUID

val exampleCommunityResponse = CommunityResponse(
    id = UUID.randomUUID(),
    name = "name",
    description = "description",
    slug = "slug",
    ownerId = "owner-id",
    membersCount = 1,
    image = ImageAsset(
        id = "https://heroheroco-assets-devel.storage.googleapis.com/images/user/herotesterznwnkkwc/1618164526274.png",
        width = 0,
        height = 0,
        fileName = null,
        fileSize = null,
        hidden = false,
    ),
    createdAt = Instant.now(),
    isVerified = false,
    isMember = false,
    owner = exampleUserResponse,
    threadsCount = 10,
    slugEditableAfter = Instant.now(),
    type = CommunityType.CONNECTED,
)

val exampleUpdateCommunitRequest = UpdateCommunityRequest(
    name = "name",
    description = "description",
    image = ImageAsset(
        id = "https://heroheroco-assets-devel.storage.googleapis.com/images/user/herotesterznwnkkwc/1618164526274.png",
        width = 0,
        height = 0,
        fileName = null,
        fileSize = null,
        hidden = false,
    ),
    slug = "slug",
    type = CommunityType.CONNECTED,
)

val examplePagedCommunityResponse = PagedCommunityResponse(
    content = listOf(exampleCommunityResponse),
    hasNext = false,
    afterCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
    beforeCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
)
