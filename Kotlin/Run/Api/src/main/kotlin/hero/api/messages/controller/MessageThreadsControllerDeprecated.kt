package hero.api.messages.controller

import com.github.kittinunf.fuel.httpGet
import hero.api.messages.controller.dto.MessageThreadDtoV2Response
import hero.api.messages.controller.dto.MessageThreadResponseIncluded
import hero.api.messages.controller.dto.messageThreadDtoExample
import hero.api.messages.controller.dto.messageThreadResponse
import hero.api.messages.service.MessageThreadService
import hero.baseutils.fetch
import hero.baseutils.instantOf
import hero.baseutils.serviceCall
import hero.contract.api.dto.MessageThreadDto
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.http4k.auth.authorizationOrNull
import hero.http4k.auth.isImpersonation
import hero.http4k.auth.parseJwtUser
import hero.http4k.config.PAGE_SIZE_MAX
import hero.http4k.controller.QueryUtils
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.patch
import hero.http4k.extensions.post
import hero.jwt.ACCESS_TOKEN
import hero.model.ListResponseMeta
import hero.model.UserDtoListResponse
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.nonEmptyString
import org.http4k.lens.string
import java.time.Instant

@Deprecated("Do not any more endpoints")
class MessageThreadsControllerDeprecated(
    messageThreadService: MessageThreadService,
) {
    @Suppress("unused")
    val routeGetThreads: ContractRoute =
        "/v1/message-threads".get(
            summary = "Lists message threads for currently authorized user.",
            tag = "Message threads",
            parameters = object {
                val authorization = Header.authorization()
                val pageIndex = QueryUtils.pageIndex()
                val pageSize = QueryUtils.pageSize()
                val include = Query.nonEmptyString()
                    .optional("include", "Entities to include. Possible values: ${listOf("lastPost,users")}")
            },
            responses = listOf(Status.OK example messageThreadResponse),
            handler = { request, parameters ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                val include = parameters.include(request)
                    ?.split(",")
                    ?.also { includes ->
                        if (includes.any { include -> include !in setOf("lastPost", "users") }) {
                            throw BadRequestException("Only 'lastPost', 'users' can be included.")
                        }
                    }
                    ?: emptyList()

                // TODO not implemented
                val pageIndex = parameters.pageIndex(request)
                val pageSize = parameters.pageSize(request)
                if (pageSize > PAGE_SIZE_MAX) {
                    throw BadRequestException("Parameter pageSize is out of bounds.", mapOf())
                }

                val (messageThreads, lastPosts) = messageThreadService.list(
                    userId = jwtUser.id,
                    includeLastPost = "lastPost" in include,
                    anonymize = request.isImpersonation(),
                    pageOffset = pageSize * pageIndex,
                    pageSize = pageSize + 1,
                )

                val usersResponse = when {
                    "users" in include ->
                        messageThreads
                            .flatMap { mt -> mt.relationships.users.map { it.id } }
                            .takeIf { it.isNotEmpty() }
                            ?.let { userIds ->
                                serviceCall("api", "/v2/users")
                                    .httpGet(
                                        listOf(
                                            "userIds" to userIds,
                                            "listDeleted" to true,
                                            "pageSize" to PAGE_SIZE_MAX,
                                        ),
                                    )
                                    .let {
                                        val authorization = request.authorizationOrNull()
                                        if (authorization != null) {
                                            it.header("Cookie", "$ACCESS_TOKEN=$authorization")
                                        } else {
                                            it
                                        }
                                    }
                                    .fetch<UserDtoListResponse>()
                            }

                    else -> null
                }

                Response(Status.OK)
                    .body(
                        MessageThreadDtoV2Response(
                            meta = ListResponseMeta(
                                pageIndex = pageIndex,
                                hasNext = messageThreads.size == pageSize + 1,
                            ),
                            messageThreads = messageThreads.take(pageSize),
                            included = MessageThreadResponseIncluded(
                                posts = lastPosts,
                                users = usersResponse?.users ?: emptyList(),
                                tiers = usersResponse?.included?.tiers ?: emptyList(),
                                categories = usersResponse?.included?.categories ?: emptyList(),
                            ),
                        ),
                    )
            },
        )

    @Suppress("unused")
    val routePostMessageThread: ContractRoute =
        "/v1/message-threads".post(
            summary = "Creates a new message thread.",
            tag = "Message threads",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = messageThreadDtoExample,
            responses = listOf(Status.OK example messageThreadDtoExample),
            handler = { request, _ ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                val requestDto = lens<MessageThreadDto>(request)
                val groupUserIds = requestDto.relationships.users.map { it.id }
                if (jwtUser.id !in groupUserIds) {
                    throw BadRequestException(
                        "${jwtUser.id} cannot start thread for a group without self in $groupUserIds",
                        mapOf("userId" to jwtUser.id),
                    )
                }
                val responseDto = messageThreadService.factory(jwtUser.id, requestDto.relationships.users.map { it.id })
                Response(Status.OK).body(responseDto)
            },
        )

    @Suppress("unused")
    val routeGetMessageThread: ContractRoute =
        ("/v1/message-threads" / Path.of("messageThreadId")).get(
            summary = "Gets existing message thread.",
            tag = "Message threads",
            parameters = object {
                val authorization = Header.authorization()
            },
            responses = listOf(Status.OK example messageThreadDtoExample),
            handler = { request, _, messageThreadId ->
                val jwtUser = request.parseJwtUser()
                    ?: throw UnauthorizedException()
                Response(Status.OK).body(
                    messageThreadService.get(
                        userId = jwtUser.id,
                        messageThreadId = messageThreadId,
                        anonymize = request.isImpersonation(),
                    ),
                )
            },
        )

    @Suppress("unused")
    val routePatchMessageThread: ContractRoute =
        ("/v1/message-threads" / Path.of("messageThreadId")).patch(
            summary = "Patches existing message thread.",
            tag = "Message threads",
            parameters = object {
                val authorization = Header.authorization()
            },
            receiving = messageThreadDtoExample,
            responses = listOf(Status.OK example messageThreadDtoExample),
            handler = { request, _, messageThreadId ->
                val jwtUser = request.parseJwtUser(allowImpersonation = false)
                    ?: throw UnauthorizedException()
                val requestDto = lens<MessageThreadDto>(request)
                if (requestDto.id != messageThreadId) {
                    throw BadRequestException("Inconsistent id in body and path: $messageThreadId != ${requestDto.id}.")
                }
                messageThreadService.patch(jwtUser.id, requestDto)
                val newDto = messageThreadService.get(
                    userId = jwtUser.id,
                    messageThreadId = messageThreadId,
                    anonymize = request.isImpersonation(),
                )
                Response(Status.OK).body(newDto)
            },
        )

    @Suppress("unused")
    val routePostMessageThreadsSeen: ContractRoute =
        ("/v1/users" / Path.userId().of("userId") / "mark-message-threads-seen").post(
            summary = "Marks message threads as seen.",
            tag = "Message threads",
            parameters = object {
                val authorization = Header.authorization()
                val seenAt = Query.string().optional("seenAt", "Timestamp of last message which was seen.")
            },
            receiving = null,
            responses = listOf(Status.NO_CONTENT example Unit),
            handler = { request, parameters, userId, _ ->
                val user = request.parseJwtUser(allowImpersonation = false) ?: throw UnauthorizedException()
                if (user.id != userId) {
                    throw ForbiddenException(
                        "Cannot mark message thread of different user as seen: ${user.id} != $userId.",
                    )
                }
                val seenAt = parameters.seenAt(request)?.let(::instantOf) ?: Instant.now()
                messageThreadService.markSeen(userId, seenAt)
                Response(Status.NO_CONTENT)
            },
        )
}
