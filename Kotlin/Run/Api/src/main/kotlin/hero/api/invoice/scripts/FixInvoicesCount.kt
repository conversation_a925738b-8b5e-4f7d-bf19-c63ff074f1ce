package hero.api.invoice.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.tryThread
import hero.gcloud.firestore
import hero.gcloud.root
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Creator
import hero.model.Invoice
import hero.model.SupportCounts
import hero.model.User

fun main() {
    val production = true
    val firestore = firestore(SystemEnv.cloudProject, production)

    val collection = firestore.typedCollectionOf(User)
    val invCollection = firestore.typedCollectionOf(Invoice)
    val list = collection
        .where(root(User::creator).path(Creator::active)).isEqualTo(true)
        .fetchAll()

    list.forEach { user ->
        val invoices = invCollection.where(Invoice::userId).isEqualTo(user.id).fetchAll().size
        tryThread {
            collection[user.id].field(root(User::counts).path(SupportCounts::invoices)).update(invoices.toLong())
            log.info("${user.id} -> $invoices")
        }
    }
}
