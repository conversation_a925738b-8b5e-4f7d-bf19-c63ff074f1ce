package hero.api.messages.controller.dto

import hero.contract.api.dto.MessageThreadDto
import hero.contract.api.dto.MessageThreadDtoAttributes
import hero.contract.api.dto.MessageThreadDtoRelationships
import hero.model.ListResponseMeta
import hero.model.PostDtoRelationship
import hero.model.SubscriptionRelationType
import hero.model.UserDtoRelationship
import java.time.Instant

val messageThreadDtoExample = MessageThreadDto(
    id = "aunwbdhland",
    attributes = MessageThreadDtoAttributes(
        createdAt = Instant.now(),
        canPost = true,
        relation = SubscriptionRelationType.IS_SUBSCRIBED_BY,
        checkedAt = Instant.now(),
        seenAt = Instant.now(),
        lastMessageAt = Instant.now(),
    ),
    relationships = MessageThreadDtoRelationships(
        users = listOf(
            UserDtoRelationship("jane-doe"),
            UserDtoRelationship("john-doe"),
        ),
        lastPost = PostDtoRelationship("last-post-id"),
    ),
)

val messageThreadResponse = MessageThreadDtoV2Response(
    ListResponseMeta(0, true),
    listOf(messageThreadDtoExample),
    MessageThreadResponseIncluded(listOf(), listOf(), listOf()),
)
