package hero.api.user.repository

import hero.exceptions.http.ServerException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.fetchAll
import hero.model.Currency
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Tier
import hero.model.TierDto
import hero.model.TierDtoAttributes
import hero.model.TierDtoRelationships

class TiersRepository(
    tiersCollection: TypedCollectionReference<Tier>,
) {
    val tiers: List<Tier> = tiersCollection.fetchAll()

    private val tierMap: Map<String, Tier> = tiers.associateBy { it.id }

    val defaultTier: Tier = tiers.find { it.default }
        ?: throw ServerException("No default Tier was specified.")

    fun getTiers(currency: Currency): List<Tier> =
        tiers
            .filter { it.currency == currency || it.id == FREE_SUBSCRIBER_TIER_ID }

    operator fun get(tierId: String) =
        tierMap[tierId]
            ?: throw ServerException("Tier $tierId not found.", mapOf("tierId" to tierId))
}

fun Tier.toDto() =
    run {
        TierDto(
            id = id,
            attributes = TierDtoAttributes(
                price = priceCents,
                default = default,
                hidden = hidden,
                currency = currency,
            ),
            relationships = TierDtoRelationships,
        )
    }
