package hero.api.post.service

import hero.api.post.service.dto.PostInput
import hero.baseutils.log
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.increment
import hero.gcloud.root
import hero.model.CommunityType
import hero.model.Post
import hero.model.PostCounts
import hero.model.Subscriber
import hero.model.topics.PostState
import hero.repository.community.JooqCommunityHelper
import hero.repository.community.fetchMemberCommunityIds
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY
import org.jooq.DSLContext
import java.time.Instant
import java.util.UUID

class CommentCommandService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postService: PostService,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
    lazyContext: Lazy<DSLContext>,
    private val logger: Logger = log,
) {
    private val context by lazyContext

    fun execute(command: CreateComment): Post {
        val parent = postsCollection[command.parentId].get()
        val rootPost = postsCollection.getRootParent(parent)
        val rootPostUserId = rootPost.userId
        val userId = command.userId

        if (parent.state == PostState.DELETED) {
            throw ConflictException("Cannot create comments for deleted post or comment")
        }

        // we allow on two levels of comments
        // parent must be either the top level post or parent's parent must be top level post
        if (parent.id != rootPost.id && parent.parentId != rootPost.id) {
            throw BadRequestException("Comments are restricted to a maximum of two levels of nesting")
        }

        val communityId = rootPost.communityId?.let { UUID.fromString(it) }
        if (userId != rootPostUserId && communityId == null) {
            subscribersCollection
                .fetchActiveSubscription(userId, rootPostUserId)
                ?: throw ForbiddenException("User $userId does not subscribe $rootPostUserId")
        }

        if (userId != rootPostUserId && communityId != null) {
            val community = context
                .selectFrom(COMMUNITY).where(COMMUNITY.ID.eq(communityId)).fetchSingle()
                .let { JooqCommunityHelper.mapRecordToEntity(it) }

            val isCommunityFree = community.type == CommunityType.FREE
            if (!isCommunityFree && communityId !in context.fetchMemberCommunityIds(userId)) {
                throw ForbiddenException("User $userId is not member of community $communityId")
            }
        }

        val comment = postService.execute(
            CreatePost(
                userId = userId,
                parentId = parent.id,
                siblingId = command.siblingId,
                parentPostId = rootPost.id,
                parentUserId = rootPostUserId,
                publishedAt = Instant.now(),
                text = command.attributes.text,
                textHtml = command.attributes.textHtml,
                textDelta = command.attributes.textDelta,
                assets = command.attributes.assets,
                isSponsored = false,
                isAgeRestricted = false,
            ),
        )
        logger.info("Created comment ${rootPost.id} by $rootPostUserId", rootPost.toLabels())

        incrementCounts(parent)
        logger.debug("Incremented counts for ${parent.id}", parent.toLabels())

        return comment
    }

    fun execute(command: UpdateComment): Post {
        val post = postService.execute(
            UpdatePost(
                userId = command.userId,
                postId = command.commentId,
                text = command.attributes.text,
                textHtml = command.attributes.textHtml,
                textDelta = command.attributes.textDelta,
                assets = command.attributes.assets,
                isAgeRestricted = false,
                isSponsored = false,
                postValidator = {
                    if (it.parentId == null) {
                        throw BadRequestException(
                            "Post ${it.id} is not a comment",
                            mapOf("userId" to command.userId, "postId" to command.commentId),
                        )
                    }

                    if (it.userId != command.userId) {
                        throw ForbiddenException("Only commenter can update his comment")
                    }
                },
            ),
        )
        logger.info("Updated comment ${post.id} by ${post.userId}", post.toLabels())

        return post
    }

    private fun incrementCounts(post: Post) {
        postsCollection[post.id]
            .field(root(Post::counts).path(PostCounts::comments))
            .increment(1)

        context
            .update(Tables.POST)
            .set(Tables.POST.COMMENTS_COUNT, Tables.POST.COMMENTS_COUNT.plus(1))
            .where(Tables.POST.ID.eq(post.id))
            .execute()

        post.parentId?.also {
            postsCollection[it]
                .field(root(Post::counts).path(PostCounts::replies))
                .increment(1)

            context
                .update(Tables.POST)
                .set(Tables.POST.REPLIES_COUNT, Tables.POST.REPLIES_COUNT.plus(1))
                .where(Tables.POST.ID.eq(it))
                .execute()
        }
    }
}

data class CreateComment(
    val userId: String,
    val parentId: String,
    val siblingId: String?,
    val attributes: PostInput,
)

data class UpdateComment(
    val userId: String,
    val commentId: String,
    val attributes: PostInput,
)
