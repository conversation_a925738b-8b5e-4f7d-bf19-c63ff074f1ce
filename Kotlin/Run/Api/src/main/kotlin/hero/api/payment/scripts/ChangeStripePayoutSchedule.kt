package hero.api.payment.scripts

import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Currency
import hero.model.Post
import hero.model.PostPayment
import hero.model.User
import hero.model.topics.PostState
import java.time.ZoneOffset

fun main() {
    val (firestore, production, _, _, clients) = initializeStripeScript(true)
    val postsCollection = firestore.typedCollectionOf(Post)
    val delayDays = 0L
    val currency = Currency.EUR

    val users = firestore.typedCollectionOf(User)
    val postPayments = firestore.typedCollectionOf(PostPayment)

    val posts = postsCollection.where(Post::price).isGreaterThan(0L)
        .fetchAll()
        .filter { it.state == PostState.PUBLISHED }
        .sortedByDescending { it.published }
        .parallelStream()
        .forEach {
            val user = users[it.userId].fetch()
            val postPayments = postPayments.where(PostPayment::postId).isEqualTo(it.id).fetchAll()
            println(
                "${it.published.atZone(ZoneOffset.UTC).toLocalDate()}\thttps://herohero.co/${user!!.path}" +
                    "\t${user.name}" +
                    "\t${it.price!!.div(100)}" +
                    "\t${postPayments.firstOrNull()?.timestamp?.atZone(ZoneOffset.UTC)?.toLocalDate() ?: ""}",
            )
        }
}
