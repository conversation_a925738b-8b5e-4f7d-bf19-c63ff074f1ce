package hero.api.user.service

import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.model.UserStore
import hero.model.topics.PostState
import hero.repository.post.PostRepository

class UserMediaCommandService(
    private val userStoresCollection: TypedCollectionReference<UserStore>,
    private val postRepository: PostRepository,
) {
    fun execute(command: UpdateAssetTimestamp) {
        val mediaStoreId = mediaStoreId(command.userId)
        val mediaStore = userStoresCollection[mediaStoreId].fetch() ?: UserStore()

        val post = postRepository.getById(command.postId)
            .takeIf { it.state !in setOf(PostState.REVISION, PostState.PROCESSING) }
            ?: throw NotFoundException("Asset ${command.assetId} does not exist")

        val asset = post.assets
            .mapNotNull { it.gjirafa }
            .firstOrNull { it.id == command.assetId }
            ?: return

        val key = if (asset.hasVideo) asset.videoStreamUrl else (asset.audioStreamUrl ?: asset.audioStaticUrl)
        if (key == null) {
            error("Failed to get key for asset ${asset.id} in post ${post.id}")
        }

        val updatedTimestamp = mediaStore.attributes.content + (key to command.timestamp)
        val updatedMediaStore = mediaStore.copy(attributes = mediaStore.attributes.copy(content = updatedTimestamp))
        userStoresCollection[mediaStoreId].set(updatedMediaStore)
    }

    private fun mediaStoreId(userId: String): String = "$userId-media"
}

/**
 * Update user's timestamp for given assetId
 */
data class UpdateAssetTimestamp(val userId: String, val assetId: String, val timestamp: Double, val postId: String)
