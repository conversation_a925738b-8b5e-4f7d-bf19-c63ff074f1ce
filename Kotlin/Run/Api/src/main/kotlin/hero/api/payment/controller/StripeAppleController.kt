package hero.api.payment.controller

import com.apple.itunes.storekit.model.NotificationTypeV2.CONSUMPTION_REQUEST
import com.apple.itunes.storekit.model.NotificationTypeV2.DID_CHANGE_RENEWAL_STATUS
import com.apple.itunes.storekit.model.NotificationTypeV2.DID_RENEW
import com.apple.itunes.storekit.model.NotificationTypeV2.EXPIRED
import com.apple.itunes.storekit.model.NotificationTypeV2.REFUND
import com.apple.itunes.storekit.model.NotificationTypeV2.SUBSCRIBED
import com.apple.itunes.storekit.model.Subtype
import hero.api.payment.controller.dto.APPLE_SANDBOX_CREATOR_ID
import hero.api.payment.controller.dto.AppleCallbackPayload
import hero.api.payment.service.AppleCallbackParser
import hero.api.payment.service.ApplePricingService
import hero.api.payment.service.StripeAppleMirroringService
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.PaymentIntentStatus.SUCCEEDED
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.baseutils.truncate
import hero.exceptions.http.ConflictException
import hero.http4k.controller.QueryUtils.userId
import hero.http4k.extensions.authorization
import hero.http4k.extensions.body
import hero.http4k.extensions.lens
import hero.http4k.extensions.post
import hero.model.Currency
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.Tier
import hero.stripe.model.AppleRequestInfo
import hero.stripe.model.Descriptors
import hero.stripe.model.InAppOperation
import hero.stripe.model.ProductItem
import hero.stripe.model.ResponseBodyV2
import hero.stripe.model.SignedPayload
import hero.stripe.model.SubscriptionCreateRequest
import hero.stripe.model.SubscriptionPeriod
import hero.stripe.service.AppleSigningService
import hero.stripe.service.AppleSubscriptionService
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Header
import org.http4k.lens.Path
import org.http4k.lens.Query
import org.http4k.lens.regex
import java.time.Duration
import java.time.Instant
import java.util.UUID

class StripeAppleController(
    private val production: Boolean,
    private val environment: String,
    private val jwtHandler: AppleCallbackParser,
    private val applePricingService: ApplePricingService,
    private val signingService: AppleSigningService,
    private val mirroringService: StripeAppleMirroringService,
    private val userRepository: UsersRepository,
    private val subscriptionService: AppleSubscriptionService,
) {
    /**
     * Webhook config: https://appstoreconnect.apple.com/apps/**********/distribution/info
     * Spec: https://developer.apple.com/documentation/appstoreservernotifications
     * Sandboxed productional testing account: https://herohero.co/applesandboxreview
     */
    @Suppress("unused")
    val routeAppleWebhook: ContractRoute =
        "/v1/stripe/apple-subscriptions".post(
            summary = "Apple webhooks handler which creates and cancels subscriptions in Herohero/Stripe.",
            parameters = object {},
            receiving = listOf(ResponseBodyV2(signedPayload = "*********")),
            tag = "Subscriptions",
            responses = listOf(Status.NO_CONTENT to Unit),
            handler = { request, _ ->
                val body = lens<ResponseBodyV2>(request)
                val payload = jwtHandler.parseAppleWebhook(body.signedPayload)
                try {
                    processApplePayload(payload)
                } catch (e: ConflictException) {
                    log.error(e.message, e.labels, e)
                }
                Response(Status.NO_CONTENT)
            },
        )

    /**
     * Endpoint called by apps to speed up creation process so that we don't have to wait
     * for slow Apple's webhooks.
     */
    @Suppress("unused")
    val routeAppleAppCallback: ContractRoute =
        "/v1/stripe/apple-app-subscriptions".post(
            summary = "iOS app callback handler to speed up subscription creation.",
            parameters = object {},
            receiving = listOf(ResponseBodyV2(signedPayload = "*********")),
            tag = "Subscriptions",
            responses = listOf(Status.NO_CONTENT to Unit, Status.UNPROCESSABLE_ENTITY to Unit),
            handler = { request, _ ->
                val body = lens<ResponseBodyV2>(request)
                val paymentStatus = try {
                    val payload = jwtHandler.parseAppCallback(body.signedPayload)
                    processApplePayload(payload)
                        ?: error("Missing generated PaymentResponse: $payload")
                } catch (e: Exception) {
                    log.error("Failing payload: ${body.signedPayload}")
                    log.fatal("Failed to parse Apple app callback: ${e.message}", cause = e)
                    return@post Response(Status.UNPROCESSABLE_ENTITY)
                }
                Response(
                    if (paymentStatus == SUCCEEDED)
                        Status.NO_CONTENT
                    else
                        Status.UNPROCESSABLE_ENTITY,
                )
            },
        )

    private fun processApplePayload(payload: AppleCallbackPayload): PaymentIntentStatus? {
        val timestamp = Instant.now()

        val logMap = mapOf(
            "appleReferenceId" to payload.requestReferenceId,
            "userId" to payload.userId,
            "creatorId" to payload.creatorId,
        )
        val durationSinceCreation = Duration.between(payload.purchaseDate, Instant.now())
            .toMillis().toBigDecimal().movePointLeft(3)

        log.info(
            "Apple notifies (created $durationSinceCreation s ago) ${payload.notificationType}: $payload",
            logMap,
        )

        if (production) {
            val isSandboxedCreator = payload.creatorId == APPLE_SANDBOX_CREATOR_ID
            // only productional subscription in sandbox must be https://herohero.co/applesandboxreview
            if (environment != "prod" && !isSandboxedCreator) {
                log.fatal(
                    "Someone is trying to subscriber a real creator in Sandbox:" +
                        " ${payload.creatorId}. See detail in logs.",
                    logMap,
                )
                throw ConflictException("Read creator payload for sandbox subscription: $payload", logMap)
            }
            if (environment == "prod" && isSandboxedCreator) {
                log.fatal(
                    "Someone is trying to subscriber a real creator in Sandbox:" +
                        " ${payload.creatorId}. See detail in logs.",
                    logMap,
                )
                throw ConflictException("Read creator payload for sandbox subscription: $payload", logMap)
            }
        }

        var paymentIntentStatus: PaymentIntentStatus? = null
        when (payload.notificationType) {
            SUBSCRIBED -> {
                // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=SUBSCRIBED,
                // subType=INITIAL_BUY, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                paymentIntentStatus = mirroringService.createSubscription(
                    appleReferenceId = payload.requestReferenceId,
                    appleTransactionId = payload.transactionId,
                    userId = payload.userId,
                    creatorId = payload.creatorId,
                    tierId = payload.tierId,
                    currencyStore = payload.currency,
                    // apple store prices is in millis
                    priceStoreCents = payload.price / 10L,
                    storefront = payload.storefront,
                ).attributes.status
            }
            REFUND -> {
                // reverse last transfer
                log.fatal("Apple requests a refund for: $payload", logMap)
                mirroringService.cancel(
                    appleReferenceId = payload.requestReferenceId,
                    appleTransactionId = payload.transactionId,
                    refund = true,
                    cancelAt = payload.expiresDate,
                )
            }
            DID_RENEW -> {
                // do a transfer to connected account
                mirroringService.storeCharge(
                    appleReferenceId = payload.requestReferenceId,
                    appleTransactionId = payload.transactionId,
                    timestamp = Instant.now(),
                    tierId = payload.tierId,
                    creatorId = payload.creatorId,
                    userId = payload.userId,
                    currencyStore = payload.currency,
                    // apple store prices is in millis
                    priceStoreCents = payload.price / 10L,
                    description = "Subscription update",
                    storefront = payload.storefront,
                )
            }
            EXPIRED -> {
                // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=EXPIRED,
                // subType=VOLUNTARY, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                mirroringService.cancel(
                    appleReferenceId = payload.requestReferenceId,
                    appleTransactionId = payload.transactionId,
                    refund = false,
                    cancelAt = payload.expiresDate,
                )
            }
            DID_CHANGE_RENEWAL_STATUS -> {
                // AppleCallbackPayload(transactionId=****************, requestReferenceId=3104c750-7c26-40ed-9bfa-2adde9cf5e49, notificationType=DID_CHANGE_RENEWAL_STATUS,
                // subType=AUTO_RENEW_DISABLED, userId=qgqotlxqyaaab, creatorId=bpsvwdhdusxof, tierId=EUR300, purchaseDate=2025-06-10T10:04:24Z, expiresDate=2025-06-10T10:07:24Z,
                // storefront=CZE, type=Auto-Renewable Subscription, price=900000, currency=CZK)
                val cancelAtPeriodEnd = when (payload.subtype) {
                    Subtype.AUTO_RENEW_ENABLED -> false
                    Subtype.AUTO_RENEW_DISABLED -> true
                    else -> error(
                        "For $DID_CHANGE_RENEWAL_STATUS we accept only RENEW subtypes, got:" +
                            " ${payload.subtype} for ${payload.requestReferenceId}",
                    )
                }
                mirroringService.appleCancelledAtPeriodEnd(
                    appleReferenceId = payload.requestReferenceId,
                    cancelAtPeriodEnd = cancelAtPeriodEnd,
                )
            }
            CONSUMPTION_REQUEST -> {
                log.fatal(
                    "User ${payload.userId} is being requested for" +
                        " consumption because of refund of ${payload.transactionId}",
                    logMap,
                )
                val user = userRepository.get(payload.userId)
                subscriptionService.answerConsumptionRequest(payload.transactionId, payload.purchaseDate, user)
            }
            else -> {
                log.fatal("Apple notification not implemented: ${payload.notificationType}: $payload", logMap)
            }
        }
        val duration = Duration.between(timestamp, Instant.now())
            .toMillis().toBigDecimal().movePointLeft(3)
        log.info("Apple processing ${payload.notificationType} took $duration s.", logMap)
        return paymentIntentStatus
    }

    @Suppress("unused")
    val routeCreateAppleSubscriptionRequest: ContractRoute =
        (
            "/v1/users" / Path.userId().of("userId", "User who subscribes.") / "subscriptions-apple" /
                Path.of("creatorId", "Creator to subscribe.")
        ).post(
            parameters = object {
                val authorization = Header.authorization()
                val storefront = Query.regex("([A-Z]{3})").required("storefront")
            },
            tag = "Subscriptions",
            summary = "Create Apple subscription request payload to be used in App.",
            receiving = null,
            responses = listOf(Status.OK to SignedPayload("*********", 499, Currency.CZK)),
            handler = { request, parameters, userId, _, creatorId ->
                val storefront = parameters.storefront(request)
                val user = userRepository.get(request, userId)
                val creator = userRepository.get(creatorId)
                if (!creator.creator.active) {
                    throw ConflictException(
                        "Creator ${creator.id} is not verified.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )
                }
                val tier = Tier.ofId(creator.creator.tierId)
                if (tier.id == FREE_SUBSCRIBER_TIER_ID) {
                    throw ConflictException(
                        "Cannot create Apple subscription for free tier.",
                        mapOf("userId" to user.id, "creatorId" to creator.id),
                    )
                }
                val productId = "${user.id.replace("-", ".")}_${creatorId.replace("-", ".")}_${tier.id}"
                val title = creator.name

                // TODO convert to two-letter country code and handle more correctly
                val appleCurrency = when (storefront) {
                    "CZE" -> Currency.CZK
                    "USA" -> Currency.USD
                    "POL" -> Currency.PLN
                    else -> Currency.EUR
                }

                val targetPriceCents = applePricingService.computeApplePriceCents(appleCurrency, tier)

                mirroringService.assertNotExisting(userId, creatorId)

                val createRequest =
                    SubscriptionCreateRequest(
                        operation = InAppOperation.CREATE_SUBSCRIPTION,
                        version = "1",
                        requestInfo = AppleRequestInfo(requestReferenceId = UUID.randomUUID().toString()),
                        currency = appleCurrency.name,
                        // TODO verify
                        // https://developer.apple.com/documentation/advancedcommerceapi/taxcodes
                        taxCode = "S021-08-1",
                        // https://developer.apple.com/documentation/advancedcommerceapi/descriptors
                        descriptors = Descriptors(
                            description = title.truncate(44),
                            displayName = title.truncate(29),
                        ),
                        period = SubscriptionPeriod.P1M,
                        storefront = storefront,
                        items = listOf(
                            ProductItem(
                                SKU = productId,
                                description = title.truncate(44),
                                displayName = title.truncate(29),
                                // price needs to be in `millis`, cents multiplied by `10`
                                price = targetPriceCents * 10,
                            ),
                        ),
                    )

                val signedPayload = signingService.signAdvancedApi(createRequest)
                Response(Status.OK)
                    .body(
                        SignedPayload(
                            signedPayload = signedPayload,
                            targetPriceCents = targetPriceCents,
                            targetCurrency = appleCurrency,
                        ),
                    )
            },
        )
}
