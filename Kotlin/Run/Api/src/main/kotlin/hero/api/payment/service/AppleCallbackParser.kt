package hero.api.payment.service

import com.apple.itunes.storekit.model.JWSRenewalInfoDecodedPayload
import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload
import com.apple.itunes.storekit.model.NotificationTypeV2
import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload
import com.apple.itunes.storekit.model.Subtype
import hero.api.payment.controller.dto.AppleCallbackPayload
import hero.baseutils.log
import hero.exceptions.http.ServerException
import hero.jackson.parseEnum
import hero.jackson.to
import hero.model.Currency
import hero.stripe.model.AdvancedCommerceInfo
import hero.stripe.service.AppleSigningService
import java.time.Instant

class AppleCallbackParser(
    private val signingService: AppleSigningService,
) {
    private val defaultAppPayload = ResponseBodyV2DecodedPayload()
        .notificationType(NotificationTypeV2.SUBSCRIBED)
        .subtype(Subtype.INITIAL_BUY)

    fun parseAppleWebhook(payload: String): AppleCallbackPayload =
        parseRawWebhook(payload)
            .let { (payload, transaction, _) -> format(payload, transaction) }

    fun parseAppCallback(payload: String): AppleCallbackPayload =
        format(defaultAppPayload, signingService.decodeTransaction(payload))

    internal fun parseRawWebhook(
        jwt: String,
    ): Triple<ResponseBodyV2DecodedPayload, JWSTransactionDecodedPayload, JWSRenewalInfoDecodedPayload?> {
        val parsed = signingService.decodeNotification(jwt)
        val transactionInfo = parsed.data.signedTransactionInfo
            ?.let { signingService.decodeTransaction(it) }
            ?: error("Apple payload did not contain transaction info: $parsed")
        val renewalInfo = parsed.data.signedRenewalInfo?.let { signingService.decodeRenewalInfo(it) }
        return Triple(parsed, transactionInfo, renewalInfo)
    }

    internal fun format(
        payload: ResponseBodyV2DecodedPayload,
        transactionInfo: JWSTransactionDecodedPayload,
    ): AppleCallbackPayload {
        val logMap = mutableMapOf("appleTransactionId" to transactionInfo.transactionId)
        try {
            val advancedCommerceInfo = transactionInfo.unknownFields["advancedCommerceInfo"]?.to<AdvancedCommerceInfo>()
            logMap["appleReferenceId"] = advancedCommerceInfo?.requestReferenceId
            val items = advancedCommerceInfo?.items
            if (items?.size != 1) {
                error("Apple subscription payload did not contain exactly 1 item: $items")
            }
            val (userId, creatorId, tierId) = items.first().SKU.split("_")
            return AppleCallbackPayload(
                transactionId = transactionInfo.transactionId,
                requestReferenceId = advancedCommerceInfo.requestReferenceId,
                notificationType = payload.notificationType,
                subtype = payload.subtype,
                // Apple productId cannot contain `-` so we replace these with `.` and here we
                // need to replace them back. This only concerns some very old userIds.
                userId = userId.replace(".", "-"),
                creatorId = creatorId.replace(".", "-"),
                tierId = tierId,
                purchaseDate = Instant.ofEpochMilli(transactionInfo.purchaseDate as Long),
                expiresDate = Instant.ofEpochMilli(transactionInfo.expiresDate as Long),
                price = transactionInfo.price,
                storefront = transactionInfo.storefront,
                type = transactionInfo.type,
                environment = transactionInfo.environment,
                currency = parseEnum<Currency>(transactionInfo.currency)
                    ?: error("Unknown currency in $transactionInfo."),
            )
        } catch (e: Throwable) {
            log.error("Failed payload: $payload", logMap)
            log.error("Failed transactionInfo: $transactionInfo", logMap)
            throw ServerException("Error when parsing Apple Payload: ${e.message} (see logs for details)", logMap, e)
        }
    }
}
