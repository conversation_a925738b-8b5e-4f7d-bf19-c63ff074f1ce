package hero.api.payment.service

import com.stripe.exception.CardException
import com.stripe.model.Coupon
import com.stripe.model.PaymentIntent
import com.stripe.param.PaymentIntentCreateParams
import com.stripe.param.PaymentIntentCreateParams.AutomaticPaymentMethods.AllowRedirects.NEVER
import hero.api.subscriber.repository.PaymentIntentStatus
import hero.api.subscriber.repository.SubscriberStripeRepository
import hero.api.user.repository.TiersRepository
import hero.baseutils.log
import hero.exceptions.http.BadRequestException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.jackson.parseEnum
import hero.model.CouponTarget
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.PaymentType
import hero.model.PostDto
import hero.model.PostPayment
import hero.model.User
import hero.model.topics.EmailPublished
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripeService
import hero.stripe.service.VatMapping
import hero.stripe.service.computeFee
import hero.stripe.service.inferOnBehalfOf
import org.http4k.core.Status
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant

class StripePaymentsService(
    private val hostname: String,
    private val hostnameServices: String,
    private val tierRepository: TiersRepository,
    private val postPaymentsCollection: TypedCollectionReference<PostPayment>,
    private val stripe: StripeService,
    private val subscriberStripeRepository: SubscriberStripeRepository,
    private val stripeCouponService: StripeCouponService,
    private val pubSub: PubSub,
    private val countryToVatMapping: VatMapping,
) {
    fun isUnlocked(
        post: PostDto,
        user: User,
    ): Boolean =
        postPaymentsCollection["pp-${post.id}-${user.id}"]
            .fetch() != null

    fun unlockPost(
        paymentMethodId: String,
        post: PostDto,
        creator: User,
        user: User,
    ): PaymentIntentWrapper {
        val tier = tierRepository[creator.creator.tierId]
        val currency = tier.currency
        val amount = post.attributes.price!!.toLong()
        val (transferCents, feePercents, feeVatCents) = computeFee(
            feePercents = tier.feePercents,
            creatorVatId = creator.company?.vatId,
            creatorCountry = creator.company?.country ?: "CZ",
            instant = Instant.now(),
            countryToVatMapping = countryToVatMapping,
        )
        if (transferCents == null) {
            error("Post unlocks are not ready in US, check coupon purchasing below.")
            // see the `TODO` below
        }
        val amountTransfer = transferCents
            .times(amount.toBigDecimal()).divide(BigDecimal(100))
            .setScale(0, RoundingMode.HALF_UP)
            .toLong()

        // note that if user has a free subscription, the stripe customer might not yet be existing
        val stripeCustomerId = subscriberStripeRepository.customerFactory(user.id, currency)

        val params = PaymentIntentCreateParams.builder()
            .setAmount(amount)
            .setPaymentMethod(paymentMethodId)
            .setCurrency(currency.name.lowercase())
            // avoid receiving needs_confirmation
            .setConfirm(true)
            .setOffSession(true)
            .setCustomer(stripeCustomerId)
            .setAutomaticPaymentMethods(
                PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                    .setEnabled(true)
                    .setAllowRedirects(NEVER)
                    .build(),
            )
            .putAllMetadata(
                mapOf(
                    "type" to PaymentType.POST_UNLOCK.name.lowercase(),
                    "creatorId" to creator.id,
                    "userId" to user.id,
                    "postId" to post.id,
                    "messageThreadId" to post.relationships.messageThread!!.id,
                    "priceCents" to amount.toString(),
                    "feePercents" to feePercents.toString(),
                    "feeVatPercents" to feeVatCents.toString(),
                    "transferCents" to amountTransfer.toString(),
                    "creatorCountry" to creator.company?.country,
                    "creatorVatId" to creator.company?.vatId,
                ),
            )
            // This would normally allow us to check for the subscription status, however this significantly slows the subscription process,
            // see https://gitlab.com/heroheroco/general/-/issues/357.
            // # .addAllExpand(listOf("latest_invoice.payment_intent"))
            // do not use .setApplicationFee to prevent fee currency conversion because of connected accounts different currencies
            // https://stripe.com/docs/connect/currencies#application-fees-for-destination-charges-and-converting-balances
            .setTransferData(
                PaymentIntentCreateParams.TransferData.builder()
                    .setDestination(creator.creator.stripeAccountId!!)
                    // this way we transfer the only the amount without our fee and so the fee will stay in original currency
                    .setAmount(amountTransfer)
                    .build(),
            )
            .setOnBehalfOf(inferOnBehalfOf(tier.currency, creator.company?.country, creator.creator.stripeAccountId!!))
            .build()

        // create a PaymentIntent with the order amount and currency
        val paymentIntent = PaymentIntentWrapper(
            try {
                stripe.createPaymentIntent(params)
            } catch (e: CardException) {
                log.error("Cannot process payment for ${user.id}: ${e.message}")
                null
            },
        )

        if (paymentIntent.status == PaymentIntentStatus.SUCCEEDED) {
            val postPayment = PostPayment(
                postId = post.id!!,
                userId = user.id,
                timestamp = paymentIntent.created ?: Instant.now(),
            )
            postPaymentsCollection[postPayment.id].set(postPayment)
        }

        return paymentIntent
    }

    fun purchaseCoupon(
        paymentMethodId: String,
        months: Int,
        creator: User?,
        user: User,
    ): Pair<PaymentIntentWrapper, Coupon?> {
        if (creator == null) {
            throw NotImplementedError("General, unbound coupons are not yet implemented.")
        }

        val couponAccountId = if (creator == null) {
            // general coupons will be stored in our temporary stripe connected account
            // to avoid with our actual incomes
            TODO("heroheroCouponAccountId")
        } else {
            // vouchers bound to creators are transferred immediately to the creator's account
            creator.creator.stripeAccountId
        }

        if (creator.creator.tierId == FREE_SUBSCRIBER_TIER_ID) {
            throw BadRequestException(
                "Cannot purchase zero-tiered coupon for creator ${creator.id} by ${user.id}",
                mapOf("creatorId" to creator.id, "userId" to user.id),
            )
        }

        val tier = tierRepository[creator.creator.tierId]

        val amountCents = tier.priceCents.toLong() * months
        val (transferPerCents, feePercents, feeVatPercents) = computeFee(
            feePercents = tier.feePercents,
            creatorVatId = creator.company?.vatId,
            creatorCountry = creator.company?.country ?: "CZ",
            instant = Instant.now(),
            countryToVatMapping = countryToVatMapping,
        )
        val transferPercents = if (creator != null) {
            // when transferring the amount to the creator, we have to transfer only the amount
            // which is relevant to its country, vat status and herohero fee
            transferPerCents?.times(amountCents.toBigDecimal())?.divide(BigDecimal(100))
                ?.setScale(0, RoundingMode.HALF_UP)
                ?.toLong()
        } else {
            // when transferring Herohero voucher amount, we take the whole amount
            amountCents
        }

        val customerId = subscriberStripeRepository.customerFactory(user.id, tier.currency)
        val couponCode = stripeCouponService.generateCouponCode(creator.id, tier.currency)
        val creatorCouponId = "${creator.id}-$couponCode"

        val params = PaymentIntentCreateParams.builder()
            .setAmount(amountCents)
            .setPaymentMethod(paymentMethodId)
            .setCurrency(tier.currency.name.lowercase())
            // avoid receiving needs_confirmation
            .setConfirm(true)
            .setOffSession(true)
            .setCustomer(customerId)
            .setDescription(
                "Payment for ${if (creator != null) "${creator.id}'s" else "Herohero's general"} coupon $couponCode",
            )
            .setTransferData(
                PaymentIntentCreateParams.TransferData.builder()
                    .setDestination(couponAccountId)
                    .setAmount(transferPercents)
                    .build(),
            )
            .setApplicationFeeAmount(if (transferPercents == null) feePercents.toLong() else null)
            .setAutomaticPaymentMethods(
                PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                    .setEnabled(true)
                    .setAllowRedirects(NEVER)
                    .build(),
            )
            // transfer group is used for associating charge with the transfer that should
            // follow in case of usage of the coupon
            .setTransferGroup(creatorCouponId)
            .setOnBehalfOf(inferOnBehalfOf(tier.currency, creator.company?.country, couponAccountId))
            .putAllMetadata(
                mapOf(
                    "type" to PaymentType.COUPON.name.lowercase(),
                    "couponType" to if (creator != null) CouponTarget.CREATOR.name else CouponTarget.HEROHERO.name,
                    "couponId" to creatorCouponId,
                    "couponAppliedForMonths" to months.toString(),
                    "creatorId" to creator.id,
                    "userId" to user.id,
                    "priceCents" to tier.priceCents.toString(),
                    "feePercents" to feePercents.toString(),
                    "feeVatPercents" to feeVatPercents.toString(),
                    "transferPercents" to transferPercents.toString(),
                    "creatorCountry" to creator.company?.country,
                    "creatorVatId" to creator.company?.vatId,
                    "appFeeHerohero" to tier.heroheroFeeAbsolute.times(months.toBigDecimal()).toString(),
                    "appFeeStripeDynamic" to tier.dynamicStripeFeeAbsolute.times(months.toBigDecimal()).toString(),
                    "appFeeStripeFixed" to tier.stripeFeeFixed.times(months.toBigDecimal()).toString(),
                    "appFeeTotal" to tier.feeAbsolute.times(months.toBigDecimal()).toString(),
                ),
            )
            .build()

        val paymentIntent = PaymentIntentWrapper(
            try {
                stripe.createPaymentIntent(params)
            } catch (e: CardException) {
                log.error("Cannot process payment for ${user.id}: ${e.message}")
                null
            },
        )

        val coupon = if (paymentIntent.status == PaymentIntentStatus.SUCCEEDED) {
            val price = subscriberStripeRepository.priceFactory(creator, tier)
            val coupon = stripeCouponService.createCoupon(
                purchasedByUserId = user.id,
                creatorId = creator.id,
                couponCode = couponCode,
                tier = tier,
                price = price,
                months = months,
                days = null,
                percentOff = null,
                campaign = "Standard voucher purchase",
                currency = tier.currency,
            )
            if (user.email != null) {
                pubSub.publish(
                    EmailPublished(
                        from = creator.name,
                        to = user.email!!,
                        template = "coupon-paid",
                        variables = listOf(
                            "user-name" to user.name,
                            "campaign" to "Standard paid voucher",
                            "creator-name" to creator.name,
                            "creator-link" to "$hostname/${creator.path}",
                            "coupon-code" to couponCode,
                            "coupon-for-months" to coupon.durationInMonths,
                            "coupon-type" to coupon.metadata["type"],
                            "coupon-link" to hostnameServices.replace(".herohero.co", "-na.herohero.co") +
                                "/gift-voucher/?creatorId=${creator.id}&couponId=$couponCode&locale=${user.language}",
                        ),
                        language = user.language,
                    ),
                )
            }
            coupon
        } else {
            null
        }

        return Pair(paymentIntent, coupon)
    }
}

data class PaymentIntentWrapper(
    val intent: PaymentIntent?,
) {
    val created: Instant? =
        intent?.created?.let { Instant.ofEpochSecond(it) }

    val status: PaymentIntentStatus = intent
        ?.let { parseEnum<PaymentIntentStatus>(it.status) }
        ?: PaymentIntentStatus.CANCELLED

    val httpStatus: Status = when (status) {
        PaymentIntentStatus.SUCCEEDED -> Status.OK
        PaymentIntentStatus.REQUIRES_ACTION -> Status.ACCEPTED
        PaymentIntentStatus.REQUIRES_CONFIRMATION -> Status.ACCEPTED
        else -> Status.UNPROCESSABLE_ENTITY
    }
}
