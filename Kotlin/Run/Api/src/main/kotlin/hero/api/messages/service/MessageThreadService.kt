package hero.api.messages.service

import com.google.cloud.firestore.Query.Direction
import hero.api.post.repository.PostRepositoryDeprecated
import hero.api.user.service.UserRelationsService
import hero.api.user.service.canInteractWithRelationAndCreators
import hero.contract.api.dto.MessageThreadDto
import hero.contract.api.dto.MessageThreadDtoAttributes
import hero.contract.api.dto.MessageThreadDtoRelationships
import hero.core.data.Sort
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.entry
import hero.gcloud.root
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.PostDto
import hero.model.PostDtoRelationship
import hero.model.SubscriptionRelationType
import hero.model.UserDtoRelationship
import hero.repository.message.MessageThreadRepository
import hero.repository.subscription.canMessage
import hero.repository.user.UserRepository
import org.jooq.DSLContext
import java.time.Instant

@Deprecated("Query part replaced by MessageThreadQueryService, Command part not done yet")
class MessageThreadService(
    lazyContext: Lazy<DSLContext>,
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val messageThreadRepository: MessageThreadRepository,
    private val userRepository: UserRepository,
    private val userRelationsService: UserRelationsService,
    private val postRepository: PostRepositoryDeprecated,
) {
    private val context by lazyContext

    fun get(
        userId: String,
        messageThreadId: String,
        includeLastPost: Boolean = true,
        anonymize: Boolean,
    ): MessageThreadDto {
        val thread = getAndAssertPrivileges(userId, messageThreadId)
        val userRelations = userRelationsService.userRelationsTo(userId, true, thread.userIds)
        val (canPost, relation, creators) = userRelations.canInteractWithRelationAndCreators(userId, thread.userIds)
        val lastPost = if (includeLastPost)
            getLastPost(
                userId = userId,
                messageThreadId = messageThreadId,
                anonymize = anonymize,
            )
        else
            null
        return thread.toDto(lastPost, canPost, userId, creators, relation)
    }

    fun factory(
        ownerUserId: String,
        userIdsParam: Collection<String>,
    ): MessageThreadDto {
        val userIds = userIdsParam.sorted().distinct()
        val userIdsToValidate = userIds - ownerUserId
        val invalidUserIds = userIdsToValidate.filter { userRepository.findById(it) == null }
        if (invalidUserIds.isNotEmpty()) {
            throw BadRequestException("User ids $invalidUserIds do not exist.")
        }

        val userRelations = userRelationsService.userRelationsTo(ownerUserId, true, userIds)
        val (canPost, relation, commonCreators) = userRelations.canInteractWithRelationAndCreators(ownerUserId, userIds)

        messageThreadsCollection
            .where(MessageThread::userIds).isEqualTo(userIds)
            .fetchSingle()
            ?.let {
                return it.toDto(
                    lastPost = getLastPost(
                        userId = ownerUserId,
                        messageThreadId = it.id,
                        anonymize = false,
                    ),
                    subscribed = canPost,
                    userId = ownerUserId,
                    commonCreators = commonCreators,
                    relation = relation,
                )
            }

        // we don't expect users to be able to message themselves yet.
        val canMessage = canMessage(context, ownerUserId, userIdsToValidate.first())
        val newThread = MessageThread(
            userIds = userIds,
            checks = mapOf(ownerUserId to Instant.now()),
            seens = mapOf(ownerUserId to Instant.now()),
            canMessage = userIds.associate { it to canMessage },
            lastMessageAt = null,
        )
        messageThreadsCollection[newThread.id].set(newThread)
        messageThreadRepository.save(newThread)

        return newThread.toDto(
            lastPost = null,
            subscribed = canPost,
            userId = ownerUserId,
            commonCreators = commonCreators,
            relation = relation,
        )
    }

    fun reactivateThread(threadId: String): MessageThread {
        val thread = messageThreadsCollection[threadId].get()
        val restoredThread = thread.copy(
            activeFor = thread.userIds,
            deletedFor = listOf(),
            archivedFor = listOf(),
        )

        messageThreadsCollection[threadId].set(restoredThread)

        return restoredThread
    }

    fun list(
        userId: String,
        includeLastPost: Boolean,
        anonymize: Boolean,
        pageSize: Int,
        pageOffset: Int,
    ): Pair<List<MessageThreadDto>, List<PostDto>> {
        val threads = messageThreadsCollection
            .where(MessageThread::activeFor).contains(userId)
            .offset(pageOffset)
            .orderBy(MessageThread::lastMessageAt, Direction.DESCENDING)
            .limit(pageSize)
            .fetchAll()

        if (threads.isEmpty()) {
            return Pair(emptyList(), emptyList())
        }

        val userIdsInThreads = threads.flatMap { it.userIds }.distinct()
        val userRelations = userRelationsService.userRelationsTo(userId, true, userIdsInThreads)

        return threads
            .map {
                val lastPost = getLastPost(userId = userId, messageThreadId = it.id, anonymize = anonymize)
                val (canPost, relation, commonCreators) = userRelations.canInteractWithRelationAndCreators(
                    userId,
                    it.userIds,
                )
                Pair(
                    it.toDto(
                        lastPost = lastPost,
                        subscribed = canPost,
                        userId = userId,
                        commonCreators = commonCreators,
                        relation = relation,
                    ),
                    lastPost,
                )
            }
            .filter { it.first.relationships.lastPost != null }
            .let { pairList ->
                Pair(
                    pairList.map { it.first },
                    if (includeLastPost) {
                        pairList.mapNotNull { it.second }
                    } else {
                        emptyList()
                    },
                )
            }
    }

    fun markSeen(
        userId: String,
        seenAt: Instant,
    ) {
        messageThreadsCollection
            .where(MessageThread::userIds).contains(userId)
            .orderBy(MessageThread::lastMessageAt, Sort.Direction.DESC)
            .fetchAll()
            .take(30)
            .forEach { thread ->
                messageThreadsCollection[thread.id]
                    .field(root(MessageThread::seens).entry(userId))
                    .update(seenAt)
            }
    }

    // this is why we shouldn't have a single patch method on entities and instead have separate methods for separate
    // business cases, such as 'markSeen, markChecked, archive, delete'.
    // this split will be done when we start using Mutations in GraphQL
    fun patch(
        userId: String,
        patchDto: MessageThreadDto,
    ) {
        // get the message thread to assert privileges
        val messageThread = getAndAssertPrivileges(userId, patchDto.id!!)
        val messageThreadId = patchDto.id
        require(messageThreadId != null)

        val checkTimestamps = patchDto.attributes.checkedAt
            ?.let { messageThread.checks + mapOf(userId to it) }
            ?: messageThread.checks

        val seenTimestamps = patchDto.attributes.seenAt
            ?.let { messageThread.seens + mapOf(userId to it) }
            ?: messageThread.seens

        val archivedFor = if (patchDto.attributes.archived == true) {
            messageThread.archivedFor.toSet() + userId
        } else if (patchDto.attributes.archived == false) {
            messageThread.archivedFor.toSet() - userId
        } else {
            messageThread.archivedFor.toSet()
        }

        val (deletedFor, deletes) = if (patchDto.attributes.deleted == true) {
            (messageThread.deletedFor + userId) to mapOf(userId to Instant.now())
        } else if (patchDto.attributes.deleted == false) {
            (messageThread.deletedFor - userId) to (messageThread.deletes)
        } else {
            (messageThread.deletedFor) to (messageThread.deletes)
        }

        val isArchived = userId in archivedFor
        val isDeleted = userId in deletedFor

        val activeFor = if (!isArchived && !isDeleted) {
            messageThread.activeFor.toSet() + userId
        } else {
            messageThread.activeFor.toSet() - userId
        }

        val updatedMessageThread = messageThread.copy(
            checks = checkTimestamps,
            seens = seenTimestamps,
            archivedFor = archivedFor.toList(),
            deletedFor = deletedFor,
            deletes = deletes,
            activeFor = activeFor.toList(),
        )

        messageThreadsCollection[updatedMessageThread.id].set(updatedMessageThread)
    }

    private fun getAndAssertPrivileges(
        userId: String,
        messageThreadId: String,
    ): MessageThread {
        val thread = messageThreadsCollection[messageThreadId].get()
        if (userId !in thread.userIds) {
            throw ForbiddenException("User $userId is not part of the message thread $messageThreadId.")
        }
        return thread
    }

    private fun MessageThread.toDto(
        lastPost: PostDto?,
        subscribed: Boolean,
        userId: String?,
        commonCreators: List<String> = emptyList(),
        relation: SubscriptionRelationType,
    ) = MessageThreadDto(
        id = id,
        attributes = MessageThreadDtoAttributes(
            createdAt = createdAt,
            archived = archivedFor.contains(userId),
            deleted = deletedFor.contains(userId),
            canPost = subscribed,
            relation = relation,
            commonCreators = commonCreators,
            seenAt = seens[userId],
            checkedAt = checks[userId],
            lastMessageAt = lastMessageAt ?: lastPost?.attributes?.publishedAt ?: createdAt,
            deletedAt = deletes[userId],
        ),
        relationships = MessageThreadDtoRelationships(
            users = userIds.map { UserDtoRelationship(it) },
            lastPost = lastPost?.let { PostDtoRelationship(it.id!!) },
        ),
    )

    private fun getLastPost(
        userId: String,
        messageThreadId: String,
        anonymize: Boolean,
    ): PostDto? =
        postRepository.getPostsByMessageThread(
            userId,
            messageThreadId = messageThreadId,
            anonymize = anonymize,
            limit = 1,
            sortingDirection = Direction.DESCENDING,
        ).firstOrNull()
}
