package hero.api.payment.controller.dto

import com.apple.itunes.storekit.model.Environment
import com.apple.itunes.storekit.model.NotificationTypeV2
import com.apple.itunes.storekit.model.Subtype
import com.apple.itunes.storekit.model.Type
import hero.model.Currency
import java.time.Instant

data class AppleCallbackPayload(
    val transactionId: String,
    /** related to advanced commerce api */
    val requestReferenceId: String,
    val notificationType: NotificationTypeV2,
    val subtype: Subtype?,
    val userId: String,
    val creatorId: String,
    val tierId: String,
    val purchaseDate: Instant,
    val expiresDate: Instant,
    val storefront: String?,
    val type: Type?,
    val price: Long,
    val currency: Currency,
    val environment: Environment,
)

// only productional subscription in sandbox must be https://herohero.co/applesandboxreview
const val APPLE_SANDBOX_CREATOR_ID = "vekwxivhnmrrk"
