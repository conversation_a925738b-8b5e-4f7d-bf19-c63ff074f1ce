package hero.api.messages.service

import com.google.firebase.messaging.AndroidConfig
import com.google.firebase.messaging.AndroidNotification
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.ApsAlert
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.api.post.service.CreatePost
import hero.api.post.service.PostService
import hero.baseutils.log
import hero.baseutils.tryThread
import hero.contract.api.dto.PostAssetInput
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.messaging.sendMulticastMessage
import hero.model.MessageThread
import hero.model.Post
import hero.repository.device.fetchFirebaseRegistrationTokens
import hero.repository.user.UserRepository
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant

class MessageCommandService(
    // todo replace with repository
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val postService: PostService,
    private val userRepository: UserRepository,
    private val firebaseMessaging: FirebaseMessaging,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: SendMessage): Post {
        validate(command)
        val messageThread = messageThreadsCollection[command.messageThreadId].get()

        if (command.userId !in messageThread.userIds) {
            throw ForbiddenException(
                "User ${command.userId} is not part of the message thread ${command.messageThreadId}",
            )
        }

        if (!messageThread.canMessage.getValue(command.userId)) {
            throw ForbiddenException(
                "User ${command.userId} cannot send message to the message thread ${command.messageThreadId}",
            )
        }

        val message = postService.execute(
            CreatePost(
                userId = command.userId,
                messageThreadId = command.messageThreadId,
                publishedAt = Instant.now(clock),
                text = command.text,
                textHtml = null,
                textDelta = null,
                assets = command.assets,
                isSponsored = false,
                isAgeRestricted = false,
                validateGjirafaAuthor = false,
                price = command.priceCents,
            ),
        )

        // TODO there is an issue with data race here, we can fix this with locking only
        // a message can come, which will take a while to be processed for unknown reasons, second message comes
        // updates the message thread, but then first message is processed and updates the message thread to old data
        messageThreadsCollection[messageThread.id].set(
            messageThread.copy(
                activeFor = messageThread.userIds,
                deletedFor = listOf(),
                archivedFor = listOf(),
                lastMessageAt = message.published,
                lastMessageBy = message.userId,
                lastMessageId = message.id,
                seens = messageThread.seens.plus(command.userId to message.published),
                checks = messageThread.checks.plus(command.userId to message.published),
            ),
        )

        val sender = userRepository.getById(command.userId)
        val otherUserIds = messageThread.userIds - command.userId
        otherUserIds.forEach { userId ->
            // refactor this so we don't have to repeat on every notification
            tryThread {

                val user = userRepository.getById(userId)
                val tokens = fetchFirebaseRegistrationTokens(context, userId)
                if (tokens.isNotEmpty()) {
                    val key = if (message.text.isNotBlank()) {
                        "push_notification_new_message_body"
                    } else {
                        "push_notification_new_asset_message"
                    }

                    val message = MulticastMessage.builder()
                        .putData("notification_type", "NEW_MESSAGE")
                        .putData("message_thread_id", messageThread.id)
                        .putData("message_id", message.id)
                        .putData("sender_id", sender.id)
                        .putData("image_sender_url", sender.image?.id ?: "")
                        .putData(
                            "image_attachment_url",
                            message.assets.firstNotNullOfOrNull { it.thumbnailImage?.id }
                                ?: message.assets.firstNotNullOfOrNull { it.image?.id }
                                ?: "",
                        )
                        .let {
                            // we still have to push silent push notifications to the apps
                            if (!user.notificationsEnabled.pushNewMessage) {
                                it
                                    .setApnsConfig(
                                        ApnsConfig.builder().setAps(
                                            Aps.builder()
                                                .setContentAvailable(true)
                                                .setMutableContent(false)
                                                .build(),
                                        ).build(),
                                    )
                                    .setAndroidConfig(
                                        AndroidConfig.builder()
                                            .setPriority(AndroidConfig.Priority.HIGH)
                                            .build(),
                                    )
                            } else {
                                it
                                    .setAndroidConfig(
                                        AndroidConfig.builder()
                                            .setNotification(
                                                AndroidNotification.builder()
                                                    .setTitle(sender.name)
                                                    .setBodyLocalizationKey(key)
                                                    .addBodyLocalizationArg(message.text)
                                                    .build(),
                                            )
                                            .build(),
                                    )
                                    .setApnsConfig(
                                        ApnsConfig.builder()
                                            .setAps(
                                                Aps.builder()
                                                    .setAlert(
                                                        ApsAlert.builder()
                                                            .setTitle(sender.name)
                                                            .setLocalizationKey(key)
                                                            .addLocalizationArg(message.text)
                                                            .build(),
                                                    )
                                                    .setContentAvailable(true)
                                                    .build(),
                                            ).build(),
                                    )
                            }
                        }

                    sendMulticastMessage(firebaseMessaging, message, tokens, userId, log)
                }
            }
        }

        return message
    }

    private fun validate(command: SendMessage) {
        if (command.text.isEmpty() && command.assets.isEmpty()) {
            throw BadRequestException("Message must have text or assets.")
        }

        if (command.priceCents != null && command.priceCents < 0) {
            throw BadRequestException("Price must be positive.")
        }

        val validAssets = command.assets.all { it.gjirafaLivestream == null && it.youtube == null }

        if (!validAssets) {
            throw BadRequestException("Invalid asset type was passed")
        }
    }
}

data class SendMessage(
    val userId: String,
    val messageThreadId: String,
    val text: String,
    val assets: List<PostAssetInput>,
    val priceCents: Long?,
)
