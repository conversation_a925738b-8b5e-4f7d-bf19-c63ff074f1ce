package hero.api.post.repository

import com.google.cloud.firestore.Query
import hero.api.post.controller.dto.PostRenderMeta
import hero.api.post.service.PostServiceDeprecated
import hero.api.post.service.toDto
import hero.api.user.service.UserRelationsService
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.CollectionQuery
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.isNull
import hero.gcloud.where
import hero.model.MessageThread
import hero.model.Post
import hero.model.PostDto
import hero.model.PostPayment
import hero.model.topics.PostState

@Deprecated("Replaced with PostService")
class PostRepositoryDeprecated(
    val postsCollection: TypedCollectionReference<Post>,
    private val messageThreadCollection: TypedCollectionReference<MessageThread>,
    private val postPaymentsCollection: TypedCollectionReference<PostPayment>,
    private val userRelationsService: UserRelationsService,
) {
    lateinit var postServiceDeprecated: PostServiceDeprecated

    private fun getPostsByUser(
        userId: String,
        categoryId: String?,
        offset: Int,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Post> =
        postsCollection
            .where(Post::userId).isEqualTo(userId)
            .and(Post::parentId).isNull()
            .and(Post::messageThreadId).isNull()
            .let {
                if (categoryId != null) {
                    it.and(Post::categories).contains(categoryId)
                } else {
                    it
                }
            }
            .and(Post::state).isIn(listOf(PostState.PROCESSING, PostState.PUBLISHED, PostState.SCHEDULED))
            .orderBy(Post::pinnedAt, Query.Direction.DESCENDING)
            .orderBy(Post::published, sortingDirection)
            .offset(offset * (limit - 1))
            .limit(limit)
            .fetchAll()

    @Deprecated("Firestore entity leaking, should produce DTO.")
    fun getPostsByUserPublic(
        jwtUserId: String?,
        userId: String,
        categoryId: String?,
        listDeleted: Boolean,
        offset: Int,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Pair<Post, PostRenderMeta>> {
        val posts = getPostsByUser(userId, categoryId, offset, limit, sortingDirection)
        val visible = jwtUserId != null &&
            userRelationsService.canInteract(
                userId = jwtUserId,
                includeCommunity = false,
                requestedUserIds = listOf(userId),
            )
        return posts.map { it to PostRenderMeta(fullResponse = visible) }
    }

    fun getPostsByParent(
        parentId: String,
        listDeleted: Boolean,
        offset: Int,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Post> =
        postsCollection
            .where(Post::parentId).isEqualTo(parentId)
            .and(Post::state)
            .isIn(
                listOf(PostState.PROCESSING, PostState.PUBLISHED) +
                    if (listDeleted) listOf(PostState.DELETED) else emptyList(),
            )
            .orderBy(Post::published, sortingDirection)
            .offset(offset * (limit - 1))
            .limit(limit)
            .fetchAll()

    @Deprecated("Firestore entity leaking, should produce DTO.")
    fun getPostsByParentPublic(
        jwtUserId: String?,
        parentId: String,
        listDeleted: Boolean,
        offset: Int,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Pair<Post, PostRenderMeta>> {
        val posts = getPostsByParent(parentId, listDeleted, offset, limit, sortingDirection)
        val visible = jwtUserId != null && isUserAllowedForComments(jwtUserId, parentId)
        return posts.map { it to PostRenderMeta(fullResponse = visible) }
    }

    fun getPostsByIds(
        postIds: List<String>,
        listDeleted: Boolean,
    ): List<Post> =
        postIds
            // INVALID_ARGUMENT: 'IN' supports up to 10 comparison values.
            .chunked(10)
            .flatMap { chunkedPostIds ->
                postsCollection
                    .where(Post::id).isIn(chunkedPostIds)
                    .run {
                        if (listDeleted) {
                            and(Post::state).isNotEqualTo(PostState.REVISION)
                        } else {
                            and(Post::state).isIn(listOf(PostState.PUBLISHED, PostState.SCHEDULED))
                        }
                    }
                    .fetchAll()
            }

    fun getPostsByIds(
        postIds: List<String>,
        includeRelated: Boolean,
        listDeleted: Boolean,
    ): List<Post> {
        val posts = getPostsByIds(postIds, listDeleted)
        if (!includeRelated) {
            return posts
        }
        val relatedPostIds =
            (posts.mapNotNull { it.parentId } + posts.mapNotNull { it.siblingId } - posts.map { it.id }).distinct()

        return posts + if (relatedPostIds.isEmpty()) {
            listOf()
        } else {
            getPostsByIds(relatedPostIds, includeRelated, listDeleted)
        }
    }

    @Deprecated("Firestore entity leaking, should produce DTO.")
    fun getPostsByIdsForPublicV1(
        jwtUserId: String?,
        postIds: List<String>,
        includeRelated: Boolean,
        listDeleted: Boolean,
    ): List<Pair<Post, PostRenderMeta>> {
        val posts = getPostsByIds(postIds, includeRelated, listDeleted)

        if (jwtUserId == null) {
            return posts.map { it to PostRenderMeta(fullResponse = false) }
        }

        // and make the post visible only if we are subscribed to the author of parent post (or it is our post), given by parentUser
        // or if it's a DM, visible only if we are subscribed to the author and message is free or user paid for the post
        // we don't bother with setting community to true here since getPostsByMessageThread should be used instead
        // if user wants to get messages by for a message thread.
        return posts.map { post ->
            val canInteract = userRelationsService.canInteract(
                jwtUserId,
                includeCommunity = false,
                requestedUserIds = listOf(post.parentUserId!!),
            )
            post to PostRenderMeta(
                fullResponse = if (post.messageThreadId != null) {
                    canInteract && publicOrPaid(post, jwtUserId)
                } else {
                    canInteract
                },
            )
        }
    }

    @Deprecated("Firestore entity leaking, should produce DTO.")
    fun getPostsByMessageThreadForPublicV1(
        jwtUserId: String?,
        messageThreadId: String,
        isImpersonation: Boolean,
        pageBefore: String?,
        pageAfter: String?,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Pair<Post, PostRenderMeta>> {
        if (jwtUserId == null) {
            throw UnauthorizedException("User is required when requesting message threads.")
        }
        val posts =
            getPostsByMessageThreadV1(jwtUserId, messageThreadId, pageAfter, pageBefore, limit, sortingDirection)
        return posts.map { post ->
            post to PostRenderMeta(
                fullResponse = publicOrPaid(post, jwtUserId),
                paymentsFromUserIds = paymentsFromUserIds(post.id),
                showText = true,
                anonymize = isImpersonation,
            )
        }
    }

    fun publicOrPaid(
        post: Post,
        userId: String,
    ): Boolean {
        // unpaid messages are public
        if ((post.price ?: 0L) == 0L) {
            return true
        }

        // I can see my own messages
        if (post.userId == userId) {
            return true
        }

        // otherwise the payment must be made
        return postPaymentsCollection["pp-${post.id}-$userId"].fetch() != null
    }

    @Deprecated("Firestore entity leaking, should produce DTO.")
    fun getPostsByMessageThreadV1(
        userId: String,
        messageThreadId: String,
        pageBefore: String? = null,
        pageAfter: String? = null,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<Post> {
        if (pageAfter != null && sortingDirection != Query.Direction.ASCENDING) {
            throw BadRequestException("When using pageAfter, sortingDirection must be ascending.")
        }
        if (pageBefore != null && sortingDirection != Query.Direction.DESCENDING) {
            throw BadRequestException("When using pageBefore, sortingDirection must be descending.")
        }
        return getPostsByMessageThreadQuery(userId, messageThreadId, pageBefore, pageAfter)
            .orderBy(Post::id, sortingDirection)
            .limit(limit)
            .fetchAll()
    }

    fun getPostsByMessageThreadQuery(
        userId: String,
        messageThreadId: String,
        pageBefore: String? = null,
        pageAfter: String? = null,
    ): CollectionQuery<Post> {
        if (userId !in getMessageThread(messageThreadId).userIds) {
            throw ForbiddenException("User $userId is not allowed to access message thread $messageThreadId.")
        }
        return postsCollection.where(Post::messageThreadId).isEqualTo(messageThreadId)
            .run { pageBefore?.let { and(Post::id).isLessThan(it) } ?: this }
            .run { pageAfter?.let { and(Post::id).isGreaterThan(it) } ?: this }
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
    }

    fun getPostsByMessageThread(
        userId: String,
        messageThreadId: String,
        anonymize: Boolean,
        pageBefore: String? = null,
        pageAfter: String? = null,
        limit: Int,
        sortingDirection: Query.Direction,
    ): List<PostDto> {
        if (pageAfter != null && sortingDirection != Query.Direction.ASCENDING) {
            throw BadRequestException("When using pageAfter, sortingDirection must be ascending.")
        }
        if (pageBefore != null && sortingDirection != Query.Direction.DESCENDING) {
            throw BadRequestException("When using pageBefore, sortingDirection must be descending.")
        }
        if (userId !in getMessageThread(messageThreadId).userIds) {
            throw ForbiddenException("User $userId is not allowed to access message thread $messageThreadId.")
        }
        return postsCollection
            .where(Post::messageThreadId).isEqualTo(messageThreadId)
            .and(Post::state).isEqualTo(PostState.PUBLISHED)
            .run { pageBefore?.let { and(Post::id).isLessThan(it) } ?: this }
            .run { pageAfter?.let { and(Post::id).isGreaterThan(it) } ?: this }
            .orderBy(Post::id, sortingDirection)
            .limit(limit)
            .fetchAll()
            .map { post ->
                post.toDto(
                    PostRenderMeta(
                        fullResponse = publicOrPaid(post, userId),
                        paymentsFromUserIds = paymentsFromUserIds(post.id),
                        showText = true,
                        anonymize = anonymize,
                    ),
                )
            }
    }

    @Deprecated(
        "This should be part of MessageThreadRepo, but causes cyclic deps. Should be split to repo and service.",
    )
    private fun getMessageThread(messageThreadId: String): MessageThread =
        messageThreadCollection[messageThreadId].fetch()
            ?: throw NotFoundException("Message thread $messageThreadId was not found.")

    fun getPost(id: String): Post =
        postsCollection[id].fetch()
            ?: throw NotFoundException("Post $id was not found.", mapOf())

    /** User is allowed to see comments if they are authors or subscribers of creator of the parent post. */
    private fun isUserAllowedForComments(
        userId: String,
        postId: String,
    ): Boolean {
        val post = getPost(postId)
        return if (post.parentId != null) {
            isUserAllowedForComments(userId, post.parentId!!)
        } else {
            userId == post.userId ||
                userRelationsService.canInteract(
                    userId = userId,
                    includeCommunity = post.messageThreadId != null,
                    requestedUserIds = listOf(post.userId),
                )
        }
    }

    fun paymentsFromUserIds(postId: String): List<String> =
        postPaymentsCollection.where(PostPayment::postId).isEqualTo(postId).fetchAll().map { it.userId }
}
