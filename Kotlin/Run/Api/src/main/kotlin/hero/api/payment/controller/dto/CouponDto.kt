package hero.api.payment.controller.dto

import hero.model.CouponMethod
import hero.model.CouponProvider
import hero.model.CouponTarget
import hero.model.TierDto
import hero.model.TierDtoRelationship
import hero.model.UserDto
import hero.model.UserDtoRelationship
import java.time.Instant

data class CouponPurchaseRequest(
    val months: Int,
    val paymentMethodId: String,
)

data class CouponInviteRequest(
    val couponCode: String?,
    /** if `months` used, `days` must not be used */
    val months: Int?,
    /** if `days` used, `months` must not be used */
    val days: Int?,
    /** if `days` used, `percentOff` must be `100` */
    val percentOff: Int,
    val redemptions: Int,
    val redeemBy: Instant?,
    // we must not allow counts higher > 100 to avoid Cloud Run timeouts
    val count: Int,
    val campaign: String,
)

data class CouponResponseDto(
    val data: CouponDto,
    val included: CouponResponseDtoIncluded,
)

data class CouponsResponseDto(
    val data: List<CouponDto>,
)

data class CouponDto(
    val id: String,
    val attributes: CouponDtoAttributes,
    val relationships: CouponDtoRelationships,
) {
    val type: String = "coupon"
}

data class CouponDtoAttributes(
    val months: Int?,
    val days: Int?,
    val percentOff: Int?,
    val redemptions: Int,
    val redeemBy: Instant?,
    val target: CouponTarget,
    val provider: CouponProvider,
    val method: CouponMethod,
    val campaign: String?,
)

data class CouponDtoRelationships(
    val tier: TierDtoRelationship?,
    val creator: UserDtoRelationship,
)

data class CouponResponseDtoIncluded(
    val tiers: List<TierDto>,
    val users: List<UserDto>,
)
