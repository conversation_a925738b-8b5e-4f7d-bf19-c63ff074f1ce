package hero.api.messages.controller.dto

import hero.contract.api.dto.MessageThreadDto
import hero.model.CategoryDto
import hero.model.ListResponseMeta
import hero.model.PostDto
import hero.model.TierDto
import hero.model.UserDto

data class MessageThreadDtoV2Response(
    val meta: ListResponseMeta,
    val messageThreads: List<MessageThreadDto>,
    val included: MessageThreadResponseIncluded,
)

data class MessageThreadResponseIncluded(
    val posts: List<PostDto> = emptyList(),
    val users: List<UserDto> = emptyList(),
    val tiers: List<TierDto> = emptyList(),
    val categories: List<CategoryDto> = emptyList(),
)
