package hero.api.payment.service

import hero.model.Currency
import hero.model.Tier
import java.math.BigDecimal
import java.math.RoundingMode

class ApplePricingService {
    // apple fee 30% + VAT 23% (worst EU case)
    private val subscriptionCoef = (1.30).toBigDecimal()
        .times((1.23).toBigDecimal())

    fun computeApplePriceCents(
        currency: Currency,
        tier: Tier,
    ): Int {
        val conversionCoef = when (currency) {
            Currency.CZK -> BigDecimal("25")
            Currency.USD -> BigDecimal("1.25")
            Currency.PLN -> BigDecimal("4.5")
            Currency.EUR -> BigDecimal.ONE
            else -> error("Unsupported currency: $currency")
        }

        val priceInTargetCurrency = tier.priceCents.toBigDecimal()
            .times(subscriptionCoef)
            .times(conversionCoef)

        val roundingLevel = when (currency) {
            Currency.CZK -> 3
            else -> 2
        }

        val oneToSubtract = when (currency) {
            Currency.CZK -> BigDecimal("100")
            else -> BigDecimal.ONE
        }

        return priceInTargetCurrency
            // shift 2 decimal places left (504 → 5.04)
            .movePointLeft(roundingLevel)
            // round up (5.04 → 6)
            .setScale(0, RoundingMode.CEILING)
            // shift back (6 → 600)
            .movePointRight(roundingLevel)
            // simulate apple pricing (600 → 599)
            .minus(oneToSubtract)
            // value is in cents, the result would be $5.99
            .toInt()
    }
}
