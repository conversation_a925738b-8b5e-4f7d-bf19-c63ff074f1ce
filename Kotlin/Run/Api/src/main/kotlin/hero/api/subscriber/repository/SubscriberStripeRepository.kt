package hero.api.subscriber.repository

import com.fasterxml.jackson.annotation.JsonProperty
import com.google.cloud.firestore.Firestore
import com.stripe.exception.InvalidRequestException
import com.stripe.exception.StripeException
import com.stripe.model.Coupon
import com.stripe.model.PaymentIntent
import com.stripe.model.Price
import com.stripe.model.Subscription
import hero.api.payment.controller.PaymentResponse
import hero.api.payment.controller.PaymentResponseAttributes
import hero.api.payment.controller.PaymentResponseRelationships
import hero.api.user.repository.TiersRepository
import hero.api.user.repository.UsersRepository
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.baseutils.retryOn
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.ServerException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.inTransaction
import hero.gcloud.root
import hero.jackson.parseEnum
import hero.model.CZ_VAT_COUNTRY
import hero.model.Creator
import hero.model.Currency
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.Tier
import hero.model.User
import hero.model.UserDtoRelationship
import hero.model.topics.CardCreateType
import hero.repository.community.addCommunityMember
import hero.repository.community.fetchOwnedCommunityIds
import hero.sql.jooq.Tables.SUBSCRIPTION
import hero.stripe.model.StripePrice
import hero.stripe.service.StripeCouponService
import hero.stripe.service.StripeService
import hero.stripe.service.StripeSubscriberSaver
import hero.stripe.service.StripeSubscriptionService
import org.jooq.DSLContext
import java.time.Instant
import java.util.concurrent.ExecutionException

class SubscriberStripeRepository(
    private val stripe: StripeService,
    private val stripeSubscriptionService: StripeSubscriptionService,
    private val stripeCouponService: StripeCouponService,
    private val stripePricesCollection: TypedCollectionReference<StripePrice>,
    private val subscriberRepository: SubscribersRepository,
    private val tierRepository: TiersRepository,
    private val userRepository: UsersRepository,
    private val stripeSubscriberSaver: StripeSubscriberSaver,
    lazyContext: Lazy<DSLContext>,
    private val firestore: Firestore,
) {
    private val context: DSLContext by lazyContext

    fun customerFactory(
        userId: String,
        currency: Currency,
    ): String =
        retryOn(ExecutionException::class) {
            firestore.runTransaction {
                val userReference = userRepository.collection[userId].inTransaction(it)
                val user = userReference.get()
                val customerId = user.customerIds[currency.name]
                if (customerId == null) {
                    val customer = stripe.createCustomer(user, currency)
                    user.customerIds[currency.name] = customer.id
                    userReference.field(User::customerIds).update(user.customerIds)
                    log.info(
                        "Created a new Stripe $currency customer ${customer.id} for ${user.id}",
                        mapOf("userId" to user.id),
                    )
                    customer.id!!
                } else {
                    customerId
                }
            }.get()
        }

    internal fun priceFactory(
        creator: User,
        tier: Tier,
    ): Price {
        if (!creator.creator.active && tier.priceCents != 0) {
            throw BadRequestException("User is not an active creator.", mapOf("userId" to creator.id))
        }
        val priceId = creator.id + "|" + tier.id

        // this is the last moment creator could have changed their Currency
        // https://gitlab.com/heroheroco/backend/-/issues/73
        // update the currency only if the creator is active and the tier is not free
        if (creator.creator.active && tier.priceCents != 0) {
            userRepository.collection[creator.id]
                .field(root(User::creator).path(Creator::currency))
                .update(tier.currency)
        }

        // first we need to select/create the "product" to subscribe
        var stripePrice = stripePricesCollection[priceId].fetch()
        if (stripePrice == null) {
            val stripePriceObject = stripeSubscriptionService.createPrice(creator, tier)
            stripePrice = StripePrice(
                priceId,
                tier.id,
                stripePriceObject.id,
            )
            stripePricesCollection[priceId].set(stripePrice)
            return stripePriceObject
        } else {
            return stripeSubscriptionService.getPrice(stripePrice.stripeId, tier.currency)
        }
    }

    fun getActiveStripeSubscription(
        user: User,
        creator: User,
        currency: Currency,
    ): Subscription? {
        // user not having stripeCustomerId is not yet subscribed to anyone
        val customerId = user.customerId(creator.creator) ?: return null

        if (!creator.creator.active) {
            throw BadRequestException("User ${creator.id} is not a creator.", mapOf("creatorId" to creator.id))
        }

        return stripeSubscriptionService
            .getSubscriptionsByCustomer(customerId, creator.id, true, currency)
            .firstOrNull()
    }

    fun resubscribe(
        user: User,
        creator: User,
        paymentMethodId: String,
        tierId: String,
        cardCreateType: CardCreateType?,
    ): PaymentResponse {
        val subscriber = subscriberRepository.getSubscriber(user.id, creator.id)
            ?: throw BadRequestException(
                "User ${user.id} cannot resubscribe ${creator.id} because" +
                    " they were not subscribed before",
            )

        if (subscriber.status.isActive) {
            throw BadRequestException(
                "User ${user.id} cannot resubscribe ${creator.id} because the subscription is still active",
            )
        }

        if (subscriber.tierId != tierId) {
            throw BadRequestException(
                "User ${user.id} cannot resubscribe ${creator.id} with tier $tierId because the original price" +
                    " was ${subscriber.tierId}",
            )
        }

        val expires = subscriber.expires
        if (expires == null || expires < Instant.now().minusDays(MAX_SUBSCRIPTION_EXPIRATION_DAYS)) {
            throw BadRequestException(
                "User ${user.id} cannot resubscribe ${creator.id} because subscription expired more than 31 days ago",
            )
        }

        return createSubscription(
            user = user,
            creator = creator,
            paymentMethodId = paymentMethodId,
            couponId = null,
            creatorTierId = tierId,
            subscribedAt = subscriber.subscribed,
            isResubscription = true,
            cardCreateType = cardCreateType,
        )
    }

    fun subscribe(
        user: User,
        creator: User,
        paymentMethodId: String?,
        couponId: String?,
        cardCreateType: CardCreateType?,
        metadata: Map<String, String?> = emptyMap(),
    ): PaymentResponse =
        createSubscription(
            user = user,
            creator = creator,
            paymentMethodId = paymentMethodId,
            couponId = couponId,
            creatorTierId = creator.creator.tierId,
            subscribedAt = Instant.now(),
            isResubscription = false,
            cardCreateType = cardCreateType,
            metadata = metadata,
        )

    private fun createSubscription(
        user: User,
        creator: User,
        paymentMethodId: String?,
        couponId: String?,
        creatorTierId: String,
        subscribedAt: Instant,
        isResubscription: Boolean,
        cardCreateType: CardCreateType?,
        metadata: Map<String, String?> = emptyMap(),
    ): PaymentResponse {
        val creatorTier = Tier.ofId(creatorTierId)
        if (paymentMethodId == null && couponId == null) {
            error("Both paymentMethodId and couponId cannot be null.")
        }
        if (couponId != null) {
            validateCouponUsage(couponId, user, creator)
        }
        var stripeSubscription = getActiveStripeSubscription(user, creator, creatorTier.currency)
        val coupon = couponId?.let { fetchCouponAndValidate(it, user, creator, creatorTier.currency) }
        val tierId = coupon?.metadata?.get("tierId") ?: creatorTierId
        val tier = tierRepository[tierId]
        if (stripeSubscription != null) {
            log.info("Stripe subscription already exists.")
        } else {
            val stripeCustomerId = customerFactory(user.id, tier.currency)
            val stripePrice = priceFactory(creator, tier)
            log.info(
                "User is creating subscription.",
                mapOf(
                    "userId" to user.id,
                    "creatorId" to creator.id,
                    "stripeCustomerId" to stripeCustomerId,
                    "priceId" to stripePrice.id,
                    "tierId" to creator.creator.tierId,
                    "paymentMethod" to paymentMethodId,
                    "couponId" to couponId,
                ),
            )

            stripeSubscription = try {
                val subscription = stripeSubscriptionService.createSubscription(
                    customerId = stripeCustomerId,
                    paymentMethodId = paymentMethodId,
                    couponId = coupon?.id,
                    tier = tier,
                    priceId = stripePrice.id,
                    creatorId = creator.id,
                    userId = user.id,
                    creatorStripeAccountId = creator.creator.stripeAccountId!!,
                    // once onboarding is refactored, this should never be null
                    creatorCountry = creator.company?.country ?: CZ_VAT_COUNTRY,
                    creatorVatId = creator.company?.vatId,
                    subscribed = subscribedAt,
                    isResubscription = isResubscription,
                    cardCreateType = cardCreateType,
                    currency = tier.currency,
                    metadata = metadata,
                )

                fetchOwnedCommunityIds(context, creator.id)
                    .forEach {
                        addCommunityMember(context, it, user.id)
                    }

                subscription
            } catch (e: InvalidRequestException) {
                val message = (e.userMessage ?: e.message ?: "") + " [${e.code}]"
                val debugMap = mapOf(
                    "userId" to user.id,
                    "creatorId" to creator.id,
                    "tierId" to tier.id,
                    "code" to e.code,
                )
                if (e.param == "discounts" && "is used up" in message) {
                    throw ConflictException(message, debugMap)
                }
                if (e.code == "coupon_expired") {
                    throw ConflictException(message, debugMap)
                }
                if ("detached from a Customer" in message) {
                    throw ForbiddenException(message, debugMap)
                }
                if ("It's possible this PaymentMethod exists on one of your connected accounts" in message) {
                    throw ForbiddenException(message, debugMap)
                }
                if ("You must collect the security code" in message) {
                    throw ForbiddenException(message, debugMap)
                }
                throw ServerException(
                    "Cannot create subscription ${user.id} -> ${creator.id} in ${tier.id}: $message [${e.requestId}]",
                    debugMap,
                    e,
                )
            }
        }

        val paymentIntentStatus: PaymentIntentStatus
        val paymentIntent: PaymentIntent?
        if (stripeSubscription.status in setOf("active", "trialing")) {
            paymentIntent = null
            paymentIntentStatus = PaymentIntentStatus.SUCCEEDED
        } else {
            val invoice = stripe.invoice(stripeSubscription.latestInvoice, tier.currency, listOf("payment_intent"))
            paymentIntent = invoice.paymentIntentObject
            paymentIntentStatus = PaymentIntentStatus.valueOf(paymentIntent.status.uppercase())
        }

        log.info(
            "Stripe Subscription ${stripeSubscription.id} for ${creator.id} has status:" +
                " ${stripeSubscription.status}/$paymentIntentStatus.",
            mapOf("userId" to user.id, "creatorId" to creator.id),
        )

        if (creator.id != stripeSubscription.metadata["creatorId"]) {
            throw BadRequestException(
                "Inconsistent creatorId when creating subscription.",
                mapOf("userId" to user.id, "creatorId" to creator.id) + stripeSubscription.metadata,
            )
        }

        val subscriber = stripeSubscriberSaver.save(user.id, creator.id, stripeSubscription, true)

        return PaymentResponse(
            PaymentResponseAttributes(
                subscriptionStatus = parseEnum<SubscriberStatus>(stripeSubscription.status),
                stripeId = stripeSubscription.id,
                status = paymentIntentStatus,
                paymentIntentClientSecret = paymentIntent?.clientSecret,
                createdAt = subscriber.subscribed,
                couponCode = null,
            ),
            PaymentResponseRelationships(
                user = UserDtoRelationship(user.id),
                post = null,
                creator = UserDtoRelationship(creator.id),
            ),
        )
    }

    private fun validateCouponUsage(
        couponId: String,
        user: User,
        creator: User,
    ) {
        val usedCoupons = context
            .select(SUBSCRIPTION.COUPON_ID)
            .from(SUBSCRIPTION)
            .where(SUBSCRIPTION.USER_ID.eq(user.id).and(SUBSCRIPTION.CREATOR_ID.eq(creator.id)))
            .mapNotNull { it[SUBSCRIPTION.COUPON_ID] }
            .toSet()

        if (couponId in usedCoupons) {
            throw ForbiddenException(
                "User ${user.id} has already used coupon $couponId",
                mapOf("userId" to user.id, "creatorId" to creator.id),
            )
        }

        if (creator.subscribersSingleCoupon && usedCoupons.isNotEmpty()) {
            throw ForbiddenException(
                "User ${user.id} has already used other coupons to subscribe to ${creator.id}",
                mapOf("userId" to user.id, "creatorId" to creator.id),
            )
        }
    }

    fun patchSubscription(
        appleReferenceId: String,
        cancelAtPeriodEnd: Boolean,
    ) {
        val subscriber = subscriberRepository.findByAppleReferenceId(appleReferenceId)
            ?: throw NotFoundException(
                "Subscription with $appleReferenceId was not found.",
                mapOf("appleReferenceId" to appleReferenceId),
            )
        val user = userRepository.get(subscriber.userId)
        val creator = userRepository.get(subscriber.creatorId)
        log.info(
            "Marking subscription of ${subscriber.id} to cancelAtPeriodEnd:$cancelAtPeriodEnd.",
            mapOf("appleReferenceId" to appleReferenceId),
        )
        patchSubscription(user, creator, subscriber, cancelAtPeriodEnd)
    }

    fun cancelSubscription(appleReferenceId: String) {
        val logMap = mutableMapOf<String, String?>("appleReferenceId" to appleReferenceId)
        val subscription = stripeSubscriptionService.findByAppleReference(appleReferenceId)
            ?: throw NotFoundException("Subscription with apple reference $appleReferenceId was not found.", logMap)
        logMap.putAll(
            mapOf(
                "userId" to subscription.metadata["userId"],
                "creatorId" to subscription.metadata["creatorId"],
            ),
        )
        log.info(
            "Cancelling subscription ${subscription.id} with apple reference $appleReferenceId.",
            logMap,
        )
        try {
            subscription.cancel()
        } catch (e: StripeException) {
            log.error("Failed to cancel subscription ${subscription.id}: ${e.message}", logMap)
        }
    }

    fun patchSubscription(
        user: User,
        creator: User,
        subscriber: Subscriber,
        cancelAtPeriodEnd: Boolean,
    ): Subscriber {
        val customerId = user.customerId(creator.creator) ?: error("Could not fetch customer id for ${user.id}")
        val tier = Tier.ofId(subscriber.tierId)
        val subscription = stripeSubscriptionService.patchSubscriptionState(
            customerId,
            creator.id,
            cancelAtPeriodEnd,
            tier.currency,
        )
            ?: throw NotFoundException(
                "Subscription was not found.",
                mapOf("userId" to user.id, "creatorId" to creator.id),
            )
        return stripeSubscriberSaver.save(user.id, creator.id, subscription, false)
    }

    private fun fetchCouponAndValidate(
        couponId: String,
        user: User,
        creator: User,
        currency: Currency,
    ): Coupon {
        val coupon = stripeCouponService.getCouponOrNull(creator.id, couponId, currency)
            ?: throw NotFoundException(
                "Coupon $couponId was not found to apply for ${creator.id} by ${user.id}.",
                mapOf("userId" to user.id, "creatorId" to creator.id),
            )

        val creatorId = coupon.metadata["creatorId"]
        if (creatorId != creator.id) {
            throw ForbiddenException(
                "Coupon ${coupon.id} is intended for $creatorId but ${user.id} tried to apply it for ${creator.id}",
                mapOf("userId" to user.id, "creatorId" to creator.id),
            )
        }
        return coupon
    }
}

enum class PaymentIntentStatus {
    @JsonProperty("requires_payment_method")
    REQUIRES_PAYMENT_METHOD,

    @JsonProperty("requires_confirmation")
    REQUIRES_CONFIRMATION,

    @JsonProperty("requires_action")
    REQUIRES_ACTION,

    @JsonProperty("processing")
    PROCESSING,

    @JsonProperty("requires_capture")
    REQUIRES_CAPTURE,

    @JsonProperty("canceled")
    CANCELLED,

    @JsonProperty("succeeded")
    SUCCEEDED,
}

private const val MAX_SUBSCRIPTION_EXPIRATION_DAYS = 31L
