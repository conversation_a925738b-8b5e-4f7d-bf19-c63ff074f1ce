package hero.api.post.service

import hero.baseutils.fromBase64
import hero.baseutils.toBase64
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.core.data.Sort
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.Post
import hero.model.topics.PostState
import hero.repository.post.JooqPostHelper
import hero.repository.post.PostType
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.USER
import hero.sql.tupleSorting
import org.jooq.DSLContext
import java.time.Instant

class FeaturedPostsQueryService(
    lazyContext: Lazy<DSLContext>,
) {
    private val context by lazyContext

    fun execute(query: GetNewPostsFromPopularCreators): Page<Post> {
        val afterCursor = query.pageable.afterCursor?.fromBase64()?.fromJson<GetNewPostsFromPopularCreatorsCursor>()

        val (posts, hasNext) = context
            .select(JooqPostHelper.postFields)
            .from(POST)
            .join(USER).on(POST.USER_ID.eq(USER.ID))
            .where(POST.STATE.eq(PostState.PUBLISHED.name))
            .and(JooqPostHelper.isCreatorPost)
            .and(POST.TYPE.eq(PostType.CONTENT_POST.name))
            .and(USER.SUBSCRIBERS_COUNT.greaterOrEqual(query.minimumSubscribers))
            .and(USER.HAS_POST_PREVIEWS.isTrue)
            .and(POST.HAS_PREVIEW.isTrue)
            .let {
                if (afterCursor != null) {
                    it.tupleSorting(
                        POST.PUBLISHED_AT,
                        POST.ID,
                        afterCursor.publishedAt,
                        afterCursor.id,
                        Sort(direction = Sort.Direction.DESC),
                    )
                } else {
                    it.orderBy(POST.PUBLISHED_AT.desc(), POST.ID.desc())
                }
            }
            .limit(query.pageable.pageSize + 1)
            .fetch()
            .let { it.map { JooqPostHelper.mapRecordToEntity(it) } }
            .let { it.take(query.pageable.pageSize) to (it.size > query.pageable.pageSize) }

        return Page(posts, nextPageable(posts, query.pageable), hasNext)
    }

    private fun nextPageable(
        posts: List<Post>,
        pageable: Pageable,
    ): Pageable {
        val afterCursor = posts
            .lastOrNull()
            ?.let {
                GetNewPostsFromPopularCreatorsCursor(it.published, it.id)
            }
            ?.toJson()
            ?.toBase64()

        return PageRequest(-1, pageable.pageSize, afterCursor = afterCursor, sort = pageable.sort)
    }
}

data class GetNewPostsFromPopularCreators(val minimumSubscribers: Int = 1000, val pageable: Pageable)

private data class GetNewPostsFromPopularCreatorsCursor(val publishedAt: Instant, val id: String)
