package hero.api.post.service

import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.increment
import hero.model.CommunityType
import hero.model.Post
import hero.repository.community.JooqCommunityHelper
import hero.repository.community.fetchMemberCommunityIds
import hero.repository.post.PostRepository
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.PostVoteRecord
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class PostVoteCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postRepository: PostRepository,
    private val postsCollection: TypedCollectionReference<Post>,
    private val clock: Clock = Clock.systemUTC(),
) {
    private val context by lazyContext

    fun execute(command: CastPostVote) {
        if (command.voteValue !in validVoteValues) {
            throw BadRequestException("Invalid vote value: $command")
        }

        val post = postRepository.getById(command.postId)
        val rootPost = postRepository.getRootPost(post)

        val community = rootPost.communityId
            ?.let { UUID.fromString(it) }
            ?.let { context.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(it)).fetchSingle() }
            ?.let { JooqCommunityHelper.mapRecordToEntity(it) }

        if (community == null) {
            throw BadRequestException("Cannot vote on creator post ${post.id}")
        }
        val isFreeCommunity = community.type == CommunityType.FREE
        if (!isFreeCommunity && community.id !in context.fetchMemberCommunityIds(command.userId)) {
            throw ForbiddenException("User ${command.userId} is not part of community ${community.id}")
        }

        val postVote = context
            .selectFrom(Tables.POST_VOTE)
            .where(Tables.POST_VOTE.USER_ID.eq(command.userId))
            .and(Tables.POST_VOTE.POST_ID.eq(command.postId))
            .fetchOne()

        // user has not changed his vote
        if (command.voteValue == postVote?.voteValue) {
            return
        }

        val postVoteRecord = PostVoteRecord().apply {
            userId = command.userId
            postId = command.postId
            voteValue = command.voteValue
            votedAt = Instant.now(clock)
            createdAt = Instant.now(clock)
            updatedAt = Instant.now(clock)
        }
        if (postVote == null) {
            context
                .insertInto(Tables.POST_VOTE)
                .set(postVoteRecord)
                .execute()

            postsCollection[command.postId].field(Post::voteScore).increment(command.voteValue.toLong())
        } else if (postVote.voteValue != command.voteValue) {
            val oldValue = postVote.voteValue
            val newValue = command.voteValue

            val updatePostVoteRecord = postVoteRecord.copy().apply {
                touched(Tables.POST_VOTE.CREATED_AT, false)
            }

            val updatedRows = context
                .update(Tables.POST_VOTE)
                .set(updatePostVoteRecord)
                .where(Tables.POST_VOTE.USER_ID.eq(command.userId))
                .and(Tables.POST_VOTE.POST_ID.eq(command.postId))
                .execute()

            if (updatedRows != 1) {
                error(
                    "Expected to update one post_vote row with for user ${command.userId} and post ${command.postId}",
                )
            }

            val changeValueBy = (newValue - oldValue).toLong()
            postsCollection[command.postId].field(Post::voteScore).increment(changeValueBy.toLong())
        }
    }
}

private val validVoteValues = setOf(1, 0, -1)

data class CastPostVote(val userId: String, val postId: String, val voteValue: Int)
