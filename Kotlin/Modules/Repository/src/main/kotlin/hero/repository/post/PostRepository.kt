package hero.repository.post

import hero.baseutils.log
import hero.core.logging.Logger
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.model.ImageAsset
import hero.model.Post
import hero.model.PostAsset
import hero.model.PostAssetType
import hero.model.PostCounts
import hero.model.topics.PostState
import hero.repository.post.JooqPostHelper.postFields
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.POST_ASSET
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import hero.sql.jooq.getSingle
import hero.sql.jooq.tables.records.PollOptionRecord
import hero.sql.jooq.tables.records.PollRecord
import hero.sql.jooq.tables.records.PostAssetRecord
import hero.sql.jooq.tables.records.PostRecord
import org.jooq.DSLContext
import org.jooq.JSONB
import org.jooq.Record
import org.jooq.Result
import org.jooq.ResultQuery
import org.jooq.SelectJoinStep
import org.jooq.SelectSelectStep
import org.jooq.impl.DSL
import org.jooq.impl.DSL.excluded
import org.jooq.impl.DSL.multiset
import java.util.UUID

class PostRepository(
    lazyContext: Lazy<DSLContext>,
    private val logger: Logger = log,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext, logger: Logger = log) : this(lazy { context }, logger)

    fun getById(postId: String): Post =
        selectPostFields(context)
            .from(POST)
            .where(POST.ID.eq(postId))
            .getSingle(JooqPostHelper::mapRecordToEntity)

    fun findById(postId: String): Post? =
        selectPostFields(context)
            .from(POST)
            .where(POST.ID.eq(postId))
            .fetchOne()
            ?.let {
                JooqPostHelper.mapRecordToEntity(it)
            }

    fun save(post: Post): Post {
        val poll = post.poll

        if (post.pollId != null && post.poll == null) {
            logger.info("Loaded post ${post.id} with poll from postgres, skipping poll saving.")
        } else {
            if (poll != null) {
                val pollRecord = PollRecord().apply {
                    id = poll.id
                    userId = post.userId
                    deadline = poll.deadline
                }
                val pollOptionRecords = poll.options.values.map {
                    PollOptionRecord().apply {
                        id = it.id
                        pollId = poll.id
                        title = it.title
                        voteCount = it.voteCount.toInt()
                        index = it.index
                    }
                }
                val pollOptionIds = poll.options.values.map { it.id }

                context.insertInto(POLL)
                    .set(pollRecord)
                    .onDuplicateKeyUpdate()
                    .set(pollRecord)
                    .execute()

                // every poll must have options, so we can skip check here
                context.insertInto(POLL_OPTION)
                    .set(pollOptionRecords)
                    .onDuplicateKeyUpdate()
                    .set(POLL_OPTION.TITLE, excluded(POLL_OPTION.TITLE))
                    .set(POLL_OPTION.VOTE_COUNT, excluded(POLL_OPTION.VOTE_COUNT))
                    .execute()

                context.deleteFrom(POLL_OPTION)
                    .where(POLL_OPTION.POLL_ID.eq(poll.id))
                    .and(POLL_OPTION.ID.notIn(pollOptionIds))
                    .execute()
            } else {
                context
                    .deleteFrom(POLL)
                    .where(POLL.ID.eq(DSL.select(POST.POLL_ID).from(POST).where(POST.ID.eq(post.id))))
                    .execute()
            }
        }

        val (postRecord, assetRecords) = mapPostToRecord(post)

        context.insertInto(POST)
            .set(postRecord)
            .onDuplicateKeyUpdate()
            .set(postRecord)
            .execute()

        context.deleteFrom(POST_ASSET)
            .where(POST_ASSET.POST_ID.eq(post.id))
            .execute()

        context.insertInto(POST_ASSET)
            .set(assetRecords)
            .execute()

        return post
    }

    fun saveAll(posts: Collection<Post>): Collection<Post> {
        posts.forEach { save(it) }

        return posts
    }

    fun getRootPost(post: Post): Post {
        if (post.parentId == null) {
            return post
        }

        val parentPostId = post.parentPostId
        if (parentPostId != null) {
            return getById(parentPostId)
        }

        val recursiveCte = DSL.name("root_post_cte")
            .`as`(
                DSL
                    .select(POST.ID, POST.PARENT_ID)
                    .from(POST)
                    .where(POST.ID.eq(post.id))
                    .unionAll(
                        DSL
                            .select(POST.ID, POST.PARENT_ID)
                            .from(POST)
                            .join(DSL.table("root_post_cte"))
                            .on(
                                POST.ID.eq(
                                    DSL.field(DSL.name("root_post_cte", POST.PARENT_ID.name), String::class.java),
                                ),
                            ),
                    ),
            )

        return context
            .withRecursive(recursiveCte)
            .select(postFields)
            .from(POST)
            .join(recursiveCte)
            .on(POST.ID.eq(recursiveCte.field(POST.ID)).and(recursiveCte.field(POST.PARENT_ID)?.isNull))
            .fetchSingle()
            .let { JooqPostHelper.mapRecordToEntity(it) }
    }

    private fun mapPostToRecord(post: Post): Pair<PostRecord, List<PostAssetRecord>> {
        val postRecord = PostRecord().apply {
            id = post.id
            userId = post.userId
            parentId = post.parentId
            siblingId = post.siblingId
            parentUserId = post.parentUserId
            parentPostId = post.parentPostId
            messageThreadId = post.messageThreadId
            communityId = post.communityId?.let { UUID.fromString(it) }

            createdAt = post.created
            updatedAt = post.updated
            publishedAt = post.published
            pinnedAt = post.pinnedAt
            notifiedAt = post.notifiedAt
            deletedAt = post.deletedAt

            state = post.state.name
            type = post.type.name
            title = post.title
            text = post.text
            textHtml = post.textHtml
            textDelta = post.textDelta
            commentsCount = post.counts.comments.toInt()
            repliesCount = post.counts.replies.toInt()
            views = post.views

            price = post.price?.toInt()
            categories = post.categories.toTypedArray()
            chapters = post.chapters?.map { JSONB.valueOf(it.toJson()) }?.toTypedArray() ?: emptyArray()

            excludeFromRss = post.excludeFromRss
            isAgeRestricted = post.isAgeRestricted
            isSponsored = post.isSponsored
            pollId = post.poll?.id
            voteScore = post.voteScore
            hasPreview = post.hasPreview
        }

        val assetsRecords = post.assets.mapNotNull {
            val metadata = it.metadata()
            val assetType = it.assetType()
            if (metadata == null || assetType == null) {
                logger.debug("Post ${post.id} has an asset which is empty")
                null
            } else {
                PostAssetRecord().apply {
                    postId = post.id
                    thumbnail = it.thumbnailImage?.id ?: it.thumbnail
                    thumbnailBlurUrl = it.thumbnailBlurUrl
                    thumbnailWidth = it.thumbnailImage?.width
                    thumbnailHeight = it.thumbnailImage?.height
                    this.metadata = metadata
                    this.assetType = assetType.name
                    this.analysis = it.analysis?.let { an -> JSONB.valueOf(an.toJson()) }
                    contentModerationMetadata = it.assetAnnotations?.let { ann -> JSONB.valueOf(ann.toJson()) }
                }
            }
        }

        return postRecord to assetsRecords
    }

    fun find(condition: SelectJoinStep<out Record>.() -> ResultQuery<out Record>): List<Post> {
        val fetchResult = selectPostFields(context)
            .from(POST)
            .condition()
            .fetch()

        return fetchResult.map { JooqPostHelper.mapRecordToEntity(it) }
    }

    fun findSingle(condition: SelectJoinStep<out Record>.() -> ResultQuery<out Record>): Post? {
        val fetchResult = selectPostFields(context)
            .from(POST)
            .condition()
            .fetchOne()

        return fetchResult?.let { JooqPostHelper.mapRecordToEntity(it) }
    }
}

private val Post.type: PostType
    get() = if (messageThreadId != null) {
        PostType.MESSAGE
    } else if (parentId != null) {
        PostType.COMMENT
    } else {
        PostType.CONTENT_POST
    }

enum class PostType {
    CONTENT_POST,
    COMMENT,
    MESSAGE,
}

// we have to use unwrap operator, but here it's always safe
private fun PostAsset.metadata() =
    when (assetType()) {
        PostAssetType.IMAGE -> JSONB.valueOf(image!!.toJson())
        PostAssetType.YOUTUBE -> JSONB.valueOf(youTube!!.toJson())
        PostAssetType.GJIRAFA -> JSONB.valueOf(gjirafa!!.toJson())
        PostAssetType.GJIRAFA_LIVESTREAM -> JSONB.valueOf(gjirafaLive!!.toJson())
        PostAssetType.DOCUMENT -> JSONB.valueOf(document!!.toJson())
        PostAssetType.BUNNY -> JSONB.valueOf(BunnyAsset(bunnyAsset!!).toJson())
        null -> null
    }

private data class BunnyAsset(
    val url: String,
)

object JooqPostHelper {
    val isCreatorPost = POST.COMMUNITY_ID.isNull
    val isThread = POST.COMMUNITY_ID.isNotNull

    fun mapRecordToEntity(record: Record): Post {
        val assets = (record[POST_ASSETS_FIELD_NAME] as Result<*>).map { asset ->
            val type = PostAssetType.valueOf(asset[POST_ASSET.ASSET_TYPE])
            val metadata = asset[POST_ASSET.METADATA]

            val thumbnailImage = if (asset[POST_ASSET.THUMBNAIL_WIDTH] != null) {
                ImageAsset(
                    id = asset[POST_ASSET.THUMBNAIL],
                    width = asset[POST_ASSET.THUMBNAIL_WIDTH],
                    height = asset[POST_ASSET.THUMBNAIL_HEIGHT],
                    // thumbnails don't store file names
                    fileName = null,
                    fileSize = null,
                )
            } else {
                null
            }

            val timestamp = if (asset.field(WATCH_ACTIVITY.TIMESTAMP) != null) {
                asset[WATCH_ACTIVITY.TIMESTAMP]
            } else {
                null
            }

            when (type) {
                PostAssetType.IMAGE -> PostAsset(image = metadata.data().fromJson())
                PostAssetType.YOUTUBE -> PostAsset(youTube = metadata.data().fromJson())
                PostAssetType.DOCUMENT -> PostAsset(document = metadata.data().fromJson())
                PostAssetType.GJIRAFA -> PostAsset(gjirafa = metadata.data().fromJson())
                PostAssetType.GJIRAFA_LIVESTREAM -> PostAsset(gjirafaLive = metadata.data().fromJson())
                PostAssetType.BUNNY -> PostAsset(bunnyAsset = metadata.data().fromJson<BunnyAsset>().url)
            }.copy(
                timestamp = timestamp,
                thumbnail = asset[POST_ASSET.THUMBNAIL],
                thumbnailBlurUrl = asset[POST_ASSET.THUMBNAIL_BLUR_URL],
                thumbnailImage = thumbnailImage,
                analysis = asset[POST_ASSET.ANALYSIS]?.data()?.fromJson(),
            )
        }

        return Post(
            id = record[POST.ID],
            assetStates = assets.mapNotNull { it.gjirafa }.map { it.status }.distinct(),
            parentId = record[POST.PARENT_ID],
            siblingId = record[POST.SIBLING_ID],
            userId = record[POST.USER_ID],
            parentPostId = record[POST.PARENT_POST_ID],
            parentUserId = record[POST.PARENT_USER_ID],
            messageThreadId = record[POST.MESSAGE_THREAD_ID],
            communityId = record[POST.COMMUNITY_ID]?.toString(),
            updated = record[POST.UPDATED_AT],
            created = record[POST.CREATED_AT],
            published = record[POST.PUBLISHED_AT],
            deletedAt = record[POST.DELETED_AT],
            pinnedAt = record[POST.PINNED_AT],
            notifiedAt = record[POST.NOTIFIED_AT],
            state = PostState.valueOf(record[POST.STATE]),
            title = record[POST.TITLE],
            text = record[POST.TEXT],
            textHtml = record[POST.TEXT_HTML],
            textDelta = record[POST.TEXT_DELTA],
            counts = PostCounts(
                comments = record[POST.COMMENTS_COUNT].toLong(),
                replies = record[POST.REPLIES_COUNT].toLong(),
            ),
            price = record[POST.PRICE]?.toLong(),
            excludeFromRss = record[POST.EXCLUDE_FROM_RSS],
            isSponsored = record[POST.IS_SPONSORED],
            isAgeRestricted = record[POST.IS_AGE_RESTRICTED],
            categories = record[POST.CATEGORIES].toList(),
            chapters = record[POST.CHAPTERS].map { jsonb -> jsonb.data().fromJson() },
            assets = assets,
            views = record[POST.VIEWS],
            assetIds = assets.mapNotNull { asset -> asset.gjirafa?.id ?: asset.gjirafaLive?.id },
            pollId = record[POST.POLL_ID],
            voteScore = record[POST.VOTE_SCORE],
            hasPreview = record[POST.HAS_PREVIEW],
        )
    }

    private val postAssetsField = multiset(
        DSL
            .select(
                POST_ASSET.METADATA,
                POST_ASSET.ASSET_TYPE,
                POST_ASSET.THUMBNAIL,
                POST_ASSET.THUMBNAIL_BLUR_URL,
                POST_ASSET.THUMBNAIL_WIDTH,
                POST_ASSET.THUMBNAIL_HEIGHT,
                POST_ASSET.ANALYSIS,
            )
            .from(POST_ASSET)
            .where(POST_ASSET.POST_ID.eq(POST.ID))
            .orderBy(POST_ASSET.ID.asc()),
    ).`as`(POST_ASSETS_FIELD_NAME)

    val postFields = listOf(
        POST.ID,
        POST.PARENT_ID,
        POST.SIBLING_ID,
        POST.USER_ID,
        POST.PARENT_POST_ID,
        POST.PARENT_USER_ID,
        POST.MESSAGE_THREAD_ID,
        POST.COMMUNITY_ID,
        POST.POLL_ID,
        POST.UPDATED_AT,
        POST.CREATED_AT,
        POST.PUBLISHED_AT,
        POST.DELETED_AT,
        POST.PINNED_AT,
        POST.NOTIFIED_AT,
        POST.STATE,
        POST.TITLE,
        POST.TEXT,
        POST.TEXT_HTML,
        POST.TEXT_DELTA,
        POST.COMMENTS_COUNT,
        POST.REPLIES_COUNT,
        POST.PRICE,
        POST.EXCLUDE_FROM_RSS,
        POST.IS_SPONSORED,
        POST.IS_AGE_RESTRICTED,
        POST.CATEGORIES,
        POST.CHAPTERS,
        POST.VIEWS,
        POST.VOTE_SCORE,
        POST.HAS_PREVIEW,
        postAssetsField,
    )

    private fun postAssetsFieldWithTimestamp(userId: String) =
        multiset(
            DSL
                .select(
                    POST_ASSET.METADATA,
                    POST_ASSET.ASSET_TYPE,
                    POST_ASSET.THUMBNAIL,
                    POST_ASSET.THUMBNAIL_BLUR_URL,
                    POST_ASSET.THUMBNAIL_WIDTH,
                    POST_ASSET.THUMBNAIL_HEIGHT,
                    POST_ASSET.ANALYSIS,
                    WATCH_ACTIVITY.TIMESTAMP,
                )
                .from(POST_ASSET)
                .leftJoin(WATCH_ACTIVITY)
                .on(
                    POST_ASSET.POST_ID.eq(WATCH_ACTIVITY.POST_ID)
                        .and(POST_ASSET.ASSET_TYPE.eq(PostAssetType.GJIRAFA.name))
                        .and(WATCH_ACTIVITY.ASSET_ID.eq(DSL.jsonbGetAttributeAsText(POST_ASSET.METADATA, "id")))
                        .and(WATCH_ACTIVITY.USER_ID.eq(userId))
                        .and(WATCH_ACTIVITY.FINISHED.isFalse)
                        .and(WATCH_ACTIVITY.DELETED_AT.isNull),
                )
                .where(POST_ASSET.POST_ID.eq(POST.ID))
                .orderBy(POST_ASSET.ID.asc()),
        ).`as`(POST_ASSETS_FIELD_NAME)

    fun postFieldsWithAssetTimestamps(userId: String?) =
        if (userId == null) {
            postFields
        } else {
            postFields - postAssetsField + postAssetsFieldWithTimestamp(userId)
        }
}

private const val POST_ASSETS_FIELD_NAME = "post_assets"

private fun selectPostFields(context: DSLContext): SelectSelectStep<Record> = context.select(postFields)
