CREATE TABLE upload_tracking
(
    id                    BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY PRIMARY KEY,
    user_id               TEXT        NOT NULL,
    request_key           TEXT        NOT NULL,
    upload_id             TEXT        NOT NULL,
    mime_type             TEXT        NOT NULL,
    parts_number          INT         NULL,
    content_length        BIGINT      NULL,
    pre_signed_url        TEXT        NULL,
    created_at            TIMESTAMPTZ NOT NULL,
    updated_at            TIMESTAMPTZ NOT NULL,
    completed_at          TIMESTAMPTZ NULL,
    aborted_at            TIMESTAMPTZ NULL,
    events                JSONB       NOT NULL,
    asset_id              TEXT        NULL,
    encoding_started_at   TIMESTAMPTZ NULL,
    encoding_completed_at TIMESTAMPTZ NULL,

    CONSTRAINT "8341e219883f479ba22150d47203f09e_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "91216b15efc34d2ebf63706642d9d996_ux" UNIQUE (request_key),
    CONSTRAINT "2c2134b33cd44649a2274741028c4cbd_ux" UNIQUE (upload_id),
    CONSTRAINT "2f874e4381634ce2b6160ee00383ef3b_ux" UNIQUE (asset_id)
);

CREATE INDEX "23d71471d2f24756853b0c711043e9d5_ix" ON upload_tracking (user_id);
CREATE INDEX "b15ffbd9f04a4394935372e539194b9e_ix" ON upload_tracking (asset_id);
CREATE INDEX "8e44c16b60ac4a6ea403023f86f334f9_ix" ON upload_tracking (created_at) WHERE completed_at IS NULL AND aborted_at IS NULL;


DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE upload_tracking TO "<EMAIL>";
        END IF;
    END
$$;
