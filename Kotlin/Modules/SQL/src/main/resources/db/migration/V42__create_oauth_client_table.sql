CREATE TABLE oauth_client
(
    id            UUID PRIMARY KEY,
    name          TEXT        NOT NULL,
    secret        TEXT        NOT NULL,
    user_id       TEXT        NOT NULL,
    redirect_uris TEXT[]      NOT NULL,
    created_at    TIMESTAMPTZ NOT NULL,
    updated_at    TIMESTAMPTZ NOT NULL,
    disabled_at   TIMESTAMPTZ NULL,
    deleted_at    TIMESTAMPTZ NULL,

    CONSTRAINT "b535a43d07a747bcb24de926b1da1c13_fk" FOREIGN KEY (user_id) REFERENCES "user" (id)
);

CREATE INDEX IF NOT EXISTS "a40798292b594b58889119a22f375540_ix" ON oauth_client (user_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE oauth_client TO "<EMAIL>";
        END IF;
    END
$$;
