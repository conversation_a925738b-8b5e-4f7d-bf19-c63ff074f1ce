DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '970216d38af44a9fb9893f392a75eadb_fk') THEN
            ALTER TABLE community_member
                ADD CONSTRAINT "970216d38af44a9fb9893f392a75eadb_fk" FOREIGN KEY (community_id) REFERENCES community (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'fc4d9c04c6d24c1f8b39913b0b6a2593_fk') THEN
            ALTER TABLE subscription
                ADD CONSTRAINT "fc4d9c04c6d24c1f8b39913b0b6a2593_fk" FOREIGN KEY (user_id) REFERENCES "user" (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '0353b74432d24d7fbae81905ac4da6d8_fk') THEN
            ALTER TABLE subscription
                ADD CONSTRAINT "0353b74432d24d7fbae81905ac4da6d8_fk" FOREIGN KEY (creator_id) REFERENCES "user" (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'a803259dcbc9424db0e5d6aa703b5dbc_fk') THEN
            ALTER TABLE post
                ADD CONSTRAINT "a803259dcbc9424db0e5d6aa703b5dbc_fk" FOREIGN KEY (message_thread_id) REFERENCES message_thread (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '9566fb1746b747d3af622fb7299574f7_fk') THEN
            ALTER TABLE post
                ADD CONSTRAINT "9566fb1746b747d3af622fb7299574f7_fk" FOREIGN KEY (parent_id) REFERENCES post (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'cdcfec234fcb4abba4f3d798622c8686_fk') THEN
            ALTER TABLE post
                ADD CONSTRAINT "cdcfec234fcb4abba4f3d798622c8686_fk" FOREIGN KEY (parent_post_id) REFERENCES post (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '0a20a03cd23348f6937f7117144857a1_fk') THEN
            ALTER TABLE post
                ADD CONSTRAINT "0a20a03cd23348f6937f7117144857a1_fk" FOREIGN KEY (parent_user_id) REFERENCES "user" (id);
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '348ba881548441ddb1666dc338b28605_fk') THEN
            ALTER TABLE post
                ADD CONSTRAINT "348ba881548441ddb1666dc338b28605_fk" FOREIGN KEY (sibling_id) REFERENCES post (id);
        END IF;
    END
$$;
