CREATE TABLE saved_post
(
    id                  TEXT PRIMARY KEY,
    user_id             TEXT        NOT NULL,
    post_id             TEXT        NOT NULL,
    creator_id          TEXT        NOT NULL,
    subscription_active BOOLEAN     NOT NULL,
    saved_at            TIMESTAMPTZ NOT NULL,
    post_published_at   TIMESTAMPTZ NOT NULL,
    deleted_at          TIMESTAMPTZ NULL,

    CONSTRAINT "52aa52823ff9455991b5def70bd31a19_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "34eddced406e4ceb97dffd13529d3f72_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "a75c8ab2589f4d91bf8bde4784c123db_fk" FOREIGN KEY (post_id) REFERENCES post (id)
);


CREATE INDEX "8f9ed433765a4cd6831f8cd1cecbba8c_ix" ON saved_post (user_id);
CREATE INDEX "d5b89e674d0d4482b25acbdd3248007b_ix" ON saved_post (saved_at);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE saved_post TO "<EMAIL>";
        END IF;
    END
$$;
