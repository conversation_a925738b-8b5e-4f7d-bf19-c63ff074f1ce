CREATE TABLE connected_account
(
    stripe_id        TEXT PRIMARY KEY,
    user_id          TEXT        NOT NULL,
    created_at       TIMESTAMPTZ NOT NULL,
    default_currency TEXT        NOT NULL,
    country          TEXT        NOT NULL,
    type             TEXT        NOT NULL,
    business_name    TEXT        NULL,
    business_email   TEXT        NULL,
    business_phone   TEXT        NULL,
    business_url     TEXT        NULL,
    business_type    TEXT        NULL

);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE connected_account TO "<EMAIL>";
        END IF;
    END
$$;
