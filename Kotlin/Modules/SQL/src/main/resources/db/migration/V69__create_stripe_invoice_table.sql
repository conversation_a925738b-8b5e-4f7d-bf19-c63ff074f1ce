CREATE TABLE stripe_invoice
(
    id                TEXT PRIMARY KEY,
    account_name      TEXT        NOT NULL,
    account_country   TEXT        NOT NULL,
    collection_method TEXT        NOT NULL,
    currency          TEXT        NOT NULL,
    customer_id       TEXT        NOT NULL,
    subscription_id   TEXT        NULL,
    payment_intent_id TEXT        NULL,
    description       TEXT        NULL,
    lines_data        JSONB       NOT NULL,
    metadata          JSONB       NOT NULL,
    period_start      TIMESTAMPTZ NOT NULL,
    period_end        TIMESTAMPTZ NOT NULL,
    status            TEXT        NULL,
    number            TEXT        NULL,
    on_behalf_of      TEXT        NULL,
    billing_reason    TEXT        NULL,
    discounts         TEXT[]      NOT NULL,
    created_at        TIMESTAMPTZ NOT NULL,
    transfer_data     JSONB       NOT NULL,
    total             INT         NOT NULL
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE stripe_invoice TO "<EMAIL>";
        END IF;
    END
$$;
