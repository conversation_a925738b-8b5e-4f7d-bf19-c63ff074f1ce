CREATE TABLE watch_activity
(
    id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    user_id             TEXT             NOT NULL,
    session_id          TEXT             NOT NULL,
    post_id             TEXT             NOT NULL,
    creator_id          TEXT             NOT NULL,
    asset_id            TEXT             NOT NULL,
    finished            BOOLEAN          NOT NULL,
    subscription_active BOOLEAN          NOT NULL,
    timestamp           DOUBLE PRECISION NOT NULL,
    created_at          TIMESTAMPTZ      NOT NULL,
    watched_at          TIMESTAMPTZ      NOT NULL,
    deleted_at          TIMESTAMPTZ      NULL,

    CONSTRAINT "b20643e03e47446b997d4a92b2d39e5f_fk" FOREIGN KEY (user_id) REFERENCES "user" (id) ON DELETE CASCADE,
    CONSTRAINT "f1178cd8201a47e590bbc6b10fea5136_fk" FOREIGN KEY (post_id) REFERENCES post (id) ON DELETE CASCADE,
    CONSTRAINT "7c092b0789a24e07ad79ed2c30e5b409_fk" FOREIGN KEY (creator_id) REFERENCES "user" (id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX "973db1906489434ea1a80b2702a00c35_ux" ON watch_activity (user_id, post_id, asset_id)
    WHERE finished = false and deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "6fa46c55f1b24bd095c5553f69c8b20b_ix" ON post_asset (asset_type);
CREATE INDEX IF NOT EXISTS "aaf30d365ad2485d8c122b2be7963a7d_ix" ON post_asset ((metadata ->> 'id'));
CREATE INDEX IF NOT EXISTS "4dce4e559c22449884c84482b238d214_ix" ON watch_activity (subscription_active);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE watch_activity TO "<EMAIL>";
        END IF;
    END
$$;
