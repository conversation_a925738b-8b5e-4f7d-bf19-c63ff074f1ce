CREATE TABLE session
(
    id               UUID PRIMARY KEY,
    user_id          TEXT        NOT NULL,
    user_agent       TEXT        NOT NULL,
    ip_address       INET        NULL,
    sign_in_location TEXT        NULL,
    created_at       TIMESTAMPTZ NOT NULL,
    refreshed_at     TIMESTAMPTZ NOT NULL,
    revoked          B<PERSON><PERSON><PERSON><PERSON>     NOT NULL,
    device_id        TEXT        NULL,
    sign_in_provider TEXT        NULL
);

CREATE INDEX "fe6816568c8b496fbd03985f180da783_ix" ON session (user_id, refreshed_at) WHERE revoked IS FALSE;

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE session TO "<EMAIL>";
        END IF;
    END
$$;
