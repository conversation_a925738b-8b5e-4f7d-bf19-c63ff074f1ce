CREATE TABLE daily_post_view_statistics
(
    id         BIGSERIAL PRIMARY KEY,
    date       DATE NOT NULL,
    post_id    TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    views      INT  NOT NULL
);

CREATE INDEX "413daf9b21a44977ba4f8a62a0880236_ix" ON daily_post_view_statistics (creator_id);
CREATE INDEX "a16743194cde40ce883ca26e0920b2b9_ix" ON daily_post_view_statistics (date);
CREATE INDEX "418717412b6e496281bf46abd2c12c2f_ix" ON daily_post_view_statistics (post_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE daily_post_view_statistics TO "<EMAIL>";
            GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO "<EMAIL>";
            ALTER DEFAULT PRIVILEGES IN SCHEMA public
                GRANT USAGE, SELECT ON SEQUENCES TO "<EMAIL>";
        END IF;
    END
$$;
