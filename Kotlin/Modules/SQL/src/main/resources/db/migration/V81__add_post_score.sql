ALTER TABLE post
    ADD COLUMN vote_score INT NOT NULL DEFAULT 0;

CREATE TABLE post_vote
(
    user_id    TEXT        NOT NULL,
    post_id    TEXT        NOT NULL,
    vote_value INT         NOT NULL CHECK (vote_value IN (-1, 0, 1)),
    voted_at   TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,

    PRIMARY KEY (user_id, post_id),
    CONSTRAINT "aaaeab1ebc3d43f18cdd37596d83cb29_fk" FOREIGN KEY (user_id) REFERENCES "user",
    CONSTRAINT "37e265f350114f26842e9ab5919ac210_fk" FOREIGN KEY (post_id) REFERENCES "post"
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE post_vote TO "<EMAIL>";
        END IF;
    END
$$;
