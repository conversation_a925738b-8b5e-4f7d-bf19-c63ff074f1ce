package hero.sql.jooq

import hero.exceptions.http.NotFoundException
import org.jooq.Record
import org.jooq.ResultQuery
import org.jooq.exception.NoDataFoundException

fun <R : Record, T> ResultQuery<R>.getSingle(mappingFunction: (R) -> T): T =
    runCatching {
        mappingFunction(fetchSingle())
    }
        .getOrElse {
            when (it) {
                is NoDataFoundException -> throw NotFoundException()
                else -> throw it
            }
        }

fun <R : Record> ResultQuery<R>.getSingle(): R =
    runCatching { fetchSingle() }
        .getOrElse {
            when (it) {
                is NoDataFoundException -> throw NotFoundException()
                else -> throw it
            }
        }
