package hero.sql.jooq

import org.jooq.SQLDialect
import org.jooq.impl.DSL
import javax.sql.DataSource

object JooqSQL {
    // we want to use [DataSource] instead of [Connection] so jooq automatically handles closed connections
    // using the data source, in our case Hikari
    // https://stackoverflow.com/a/62693974
    fun context(dataSource: DataSource) = DSL.using(dataSource, SQLDialect.POSTGRES)
}
