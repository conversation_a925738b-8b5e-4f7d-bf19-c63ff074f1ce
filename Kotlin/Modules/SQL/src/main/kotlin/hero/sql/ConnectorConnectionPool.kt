package hero.sql

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import hero.baseutils.ServiceType
import hero.baseutils.SystemEnv
import hero.baseutils.systemEnv
import hero.baseutils.systemEnvRelaxed
import org.postgresql.Driver
import java.util.Properties
import javax.sql.DataSource

object ConnectorConnectionPool {
    private val hikariDataSource: HikariDataSource by lazy {
        createConnectionPool()
    }

    val dataSource: DataSource by lazy {
        hikariDataSource
    }

    fun stats(): PoolConnectionStats {
        val totalConnections = hikariDataSource.hikariPoolMXBean.totalConnections
        val activeConnections = hikariDataSource.hikariPoolMXBean.activeConnections
        val idleConnections = hikariDataSource.hikariPoolMXBean.idleConnections

        return PoolConnectionStats(
            totalConnections = totalConnections,
            activeConnections = activeConnections,
            idleConnections = idleConnections,
        )
    }

    private fun createConnectionPool(): HikariDataSource {
        val instanceName = systemEnv(SQL_INSTANCE_NAME)
        val databaseName = systemEnv(SQL_DATABASE_NAME)
        val unixSocket = systemEnvRelaxed(SQL_PROXY_ENV)
        val localDevelopment = SystemEnv.environment == "local" && unixSocket == null

        val connProps = Properties().apply {
            if (localDevelopment) {
                setProperty("user", "postgres")
                setProperty("password", "postgres")
            } else {
                setProperty("user", "<EMAIL>")
                // Note: a non-empty string value for the password property must be set.
                // While this property will be ignored when connecting with the Cloud SQL Connector using IAM auth,
                // leaving it empty will cause driver-level validations to fail.
                // https://github.com/GoogleCloudPlatform/cloud-sql-jdbc-socket-factory/blob/main/docs/jdbc.md
                setProperty("password", "pwd")
                setProperty("sslmode", "disable")
                setProperty("socketFactory", "com.google.cloud.sql.postgres.SocketFactory")
                setProperty("cloudSqlInstance", instanceName)
                setProperty("enableIamAuth", "true")
                // https://github.com/brettwooldridge/HikariCP/issues/1474#issuecomment-2258003684
                // Connection pool sometimes gets empty and does not recover
                setProperty("tcpKeepAlive", "true")
                // https://github.com/GoogleCloudPlatform/cloud-sql-jdbc-socket-factory/blob/main/docs/jdbc.md#refresh-strategy-for-serverless-compute
                setProperty("cloudSqlRefreshStrategy", "lazy")
            }
        }

        // Cloud SQL imposes a maximum limit on concurrent connections,
        // and these limits may vary depending on the database engine chosen (see Cloud SQL Quotas and Limits).
        // It's recommended to use a connection with Cloud Functions,
        // but it is important to set the maximum number of connections to 1.
        // https://cloud.google.com/sql/docs/mysql/connect-functions#connection-limits
        val maxPoolSize = when (SystemEnv.serviceType) {
            ServiceType.CLOUD_RUN -> systemEnvRelaxed("SQL_MAX_POOL_SIZE")?.toIntOrNull() ?: 10
            ServiceType.CLOUD_FUNCTION -> 1
            null -> 5
        }

        val serverAddress = if (localDevelopment) "localhost:5432" else ""
        val applicationName = "${SystemEnv.environment}-${systemEnvRelaxed("SERVICE_NAME") ?: "undefined"}"
        val config = HikariConfig().apply {
            maximumPoolSize = maxPoolSize
            // With 30 minutes, we saw these errors (This connection has been closed.). Possibly consider using a shorter maxLifetime value
            maxLifetime = 600_000 // 10 minutes
            idleTimeout = 300_000 // 5 minutes
            // https://jdbc.postgresql.org/documentation/use/
            jdbcUrl = "*******************************************************************************"
            if (unixSocket != null) {
                addDataSourceProperty("unixSocketPath", unixSocket)
            }
            driverClassName = Driver::class.java.name
            dataSourceProperties = connProps
            connectionTimeout = 30000 // 30s
        }

        return HikariDataSource(config)
    }
}

data class PoolConnectionStats(
    val totalConnections: Int,
    val activeConnections: Int,
    val idleConnections: Int,
)

private const val SQL_DATABASE_NAME = "SQL_DATABASE_NAME"
private const val SQL_PROXY_ENV = "SQL_PROXY_UNIX_SOCKET"
private const val SQL_INSTANCE_NAME = "SQL_INSTANCE_NAME"
