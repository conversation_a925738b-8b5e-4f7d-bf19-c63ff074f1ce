package hero.model

import hero.core.annotation.NoArg

@NoArg
sealed class SealedOAuthCredentials {
    abstract val appId: String
    abstract val appSecret: String
}

@NoArg
data class OAuthCredentials(
    override val appId: String,
    override val appSecret: String,
) : SealedOAuthCredentials()

@NoArg
data class SpotifyCredentials(
    override val appId: String,
    override val appSecret: String,
    val partnerId: String,
    val rssFeedToken: String,
) : SealedOAuthCredentials()

@NoArg
data class DiscordCredentials(
    override val appId: String,
    override val appSecret: String,
    val botToken: String,
) : SealedOAuthCredentials()
