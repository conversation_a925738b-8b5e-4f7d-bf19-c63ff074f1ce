package hero.stripe.service

import com.apple.itunes.storekit.model.AccountTenure
import com.apple.itunes.storekit.model.ConsumptionRequest
import com.apple.itunes.storekit.model.ConsumptionStatus
import com.apple.itunes.storekit.model.DeliveryStatus
import com.apple.itunes.storekit.model.LifetimeDollarsPurchased
import com.apple.itunes.storekit.model.LifetimeDollarsRefunded
import com.apple.itunes.storekit.model.Platform
import com.apple.itunes.storekit.model.PlayTime
import com.apple.itunes.storekit.model.RefundPreference
import com.apple.itunes.storekit.model.Status
import com.apple.itunes.storekit.model.StatusResponse
import com.github.kittinunf.fuel.core.Response
import com.github.kittinunf.fuel.core.extensions.cUrlString
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import com.github.kittinunf.fuel.httpPut
import hero.baseutils.SystemEnv
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.jackson.toJson
import hero.model.User
import hero.model.UserStatus
import hero.stripe.model.AppleRefundType
import hero.stripe.model.AppleRequestInfo
import hero.stripe.model.AppleSubscriptionCancelRequest
import hero.stripe.model.AppleSubscriptionRevokeRequest
import java.time.Duration
import java.time.Instant

class AppleSubscriptionService(
    private val signingService: AppleSigningService,
    private val isProduction: Boolean,
    private val storeKitPath: String = if (isProduction) "storekit" else "storekit-sandbox",
    private val itunesHost: String = "https://api.$storeKitPath.itunes.apple.com",
) {
    // TODO https://github.com/apple/app-store-server-library-java/issues/169

    // Immediate cancel and refund.
    // https://developer.apple.com/documentation/advancedcommerceapi/revoke-subscription
    fun cancel(
        appleTransactionId: String,
        appleReferenceId: String,
        refundReason: String,
        storefront: String,
    ): Response {
        val payload = AppleSubscriptionRevokeRequest(
            refundReason = refundReason,
            refundRiskingPreference = true,
            refundType = AppleRefundType.FULL,
            requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
            storefront = storefront,
        )
        val signature = signingService.signAppStoreConnect(payload)

        return "$itunesHost/advancedCommerce/v1/subscription/revoke/$appleTransactionId"
            .httpPost()
            .header("Authorization", "Bearer $signature")
            .header("Content-Type", "application/json")
            .body(payload.toJson())
            .also { println(it.cUrlString()) }
            .response()
            .second
    }

    // Cancel at a period end.
    // https://developer.apple.com/documentation/advancedcommerceapi/cancel-a-subscription
    fun cancelAtPeriodEnd(
        appleTransactionId: String,
        appleReferenceId: String,
        storefront: String,
    ): Response {
        val payload = AppleSubscriptionCancelRequest(
            requestInfo = AppleRequestInfo(requestReferenceId = appleReferenceId),
            storefront = storefront,
        )
        val signature = signingService.signAppStoreConnect(payload)

        // signing app store requests https://developer.apple.com/documentation/storekit/generating-jws-to-sign-app-store-requests
        return "$itunesHost/advancedCommerce/v1/subscription/cancel/$appleTransactionId"
            .httpPost()
            .header("Authorization", "Bearer $signature")
            .header("Content-Type", "application/json")
            .body(payload.toJson())
            .also { println(it.cUrlString()) }
            .response()
            .second
    }

    /**
     * Send consumption information about a consumable in-app purchase or auto-renewable subscription to the App Store
     * after your server receives a consumption request notification. This is used when user requests a refund to
     * evaluate refund eligibility.
     */
    fun answerConsumptionRequest(
        appleTransactionId: String,
        subscribedSince: Instant,
        user: User,
    ) {
        val subscribedDays = Duration.between(subscribedSince, Instant.now()).toDays()

        // https://developer.apple.com/documentation/appstoreserverapi/consumptionrequest
        val payload = ConsumptionRequest()
            .accountTenure(
                when (Duration.between(user.created, Instant.now()).toDays()) {
                    in 0..3 -> AccountTenure.ZERO_TO_THREE_DAYS
                    in 3..10 -> AccountTenure.THREE_DAYS_TO_TEN_DAYS
                    in 10..30 -> AccountTenure.TEN_DAYS_TO_THIRTY_DAYS
                    in 30..90 -> AccountTenure.THIRTY_DAYS_TO_NINETY_DAYS
                    in 90..180 -> AccountTenure.NINETY_DAYS_TO_ONE_HUNDRED_EIGHTY_DAYS
                    in 180..365 -> AccountTenure.ONE_HUNDRED_EIGHTY_DAYS_TO_THREE_HUNDRED_SIXTY_FIVE_DAYS
                    else -> AccountTenure.GREATER_THAN_THREE_HUNDRED_SIXTY_FIVE_DAYS
                },
            )
            // https://developer.apple.com/documentation/appstoreserverapi/appaccounttoken
            // The appAccountToken value may be an empty string if your app doesn’t use app account tokens.
            .appAccountToken(null)
            .consumptionStatus(ConsumptionStatus.PARTIALLY_CONSUMED)
            .deliveryStatus(DeliveryStatus.DELIVERED_AND_WORKING_PROPERLY)
            .lifetimeDollarsPurchased(LifetimeDollarsPurchased.UNDECLARED)
            .lifetimeDollarsRefunded(LifetimeDollarsRefunded.UNDECLARED)
            .platform(Platform.UNDECLARED)
            .playTime(
                when (subscribedDays) {
                    in 0..1 -> PlayTime.FIVE_TO_SIXTY_MINUTES
                    in 2..10 -> PlayTime.ONE_TO_SIX_HOURS
                    else -> PlayTime.SIX_HOURS_TO_TWENTY_FOUR_HOURS
                },
            )
            .refundPreference(
                when {
                    subscribedDays < 2 -> RefundPreference.PREFER_GRANT
                    subscribedDays > 7 -> RefundPreference.PREFER_DECLINE
                    else -> RefundPreference.UNDECLARED
                },
            )
            .customerConsented(true)
            .sampleContentProvided(true)
            .userStatus(
                if (user.status == UserStatus.ACTIVE)
                    com.apple.itunes.storekit.model.UserStatus.ACTIVE
                else
                    com.apple.itunes.storekit.model.UserStatus.TERMINATED,
            )

        val signature = signingService.signAppStoreConnect(payload)

        val response =
            "$itunesHost/inApps/v1/transactions/consumption/$appleTransactionId"
                .httpPut()
                .header("Authorization", "Bearer $signature")
                .header("Content-Type", "application/json")
                .body(payload.toJson())
                .response()
                .second

        if (response.statusCode != 202) {
            log.fatal("Apple refused consumption response for ${user.id}/$appleTransactionId: $response")
        }
    }

    fun status(transactionId: String): List<Status> {
        val signature = signingService.signAppStoreConnect(mapOf<String, Any>())
        return "$itunesHost/inApps/v1/subscriptions/$transactionId"
            .httpGet()
            .header("Authorization", "Bearer $signature")
            .fetch<StatusResponse>()
            .data
            .flatMap { it.lastTransactions }
            .map { it.status }
    }
}

fun main() {
    val service = AppleSubscriptionService(AppleSigningService(false, "devel"), false)
    val firestore = firestore(SystemEnv.cloudProject, false)
    val users = firestore.typedCollectionOf(User)
    val user = users["bxwcagimmnijj"].get()

    service.answerConsumptionRequest("2000001047125991", Instant.now().minusDays(10), user)

    val respo1 = service.cancel("2000000943274226", "9df55a88-28be-4a3d-ac9d-272caad562ec", "please", "cze")
    println(respo1)

    val respo2 = service.cancelAtPeriodEnd("2000000943274226", "9df55a88-28be-4a3d-ac9d-272caad562ec", "cze")
    println(respo2)
}
