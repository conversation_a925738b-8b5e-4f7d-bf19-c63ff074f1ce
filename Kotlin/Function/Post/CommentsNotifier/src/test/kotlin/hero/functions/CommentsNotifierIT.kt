package hero.functions

import hero.model.GjirafaLiveAsset
import hero.model.LiveVideoStatus
import hero.model.Notification
import hero.model.NotificationType
import hero.model.PostAsset
import hero.model.StorageEntityType
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.seconds

class CommentsNotifierIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1741610376)

    @Test
    fun `commenting a post should create NEW_COMMENT notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("johny", parentId = post.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "cestmir",
                type = NotificationType.NEW_COMMENT,
                actorIds = listOf("johny"),
                objectType = StorageEntityType.POST,
                objectId = comment.id,
                created = expectedTimestamp,
                timestamp = expectedTimestamp,
                seenAt = null,
                checkedAt = null,
                id = "cestmir-1741610376",
            ),
        )
    }

    @Test
    fun `commenting on a thread should create NEW_COMMENT notification with community id set`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir")

        val post = testHelper.createPost("cestmir", communityId = community.id)
        val comment = testHelper.createPost("johny", parentId = post.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "cestmir",
                type = NotificationType.NEW_COMMENT,
                actorIds = listOf("johny"),
                objectType = StorageEntityType.POST,
                objectId = comment.id,
                created = expectedTimestamp,
                timestamp = expectedTimestamp,
                seenAt = null,
                checkedAt = null,
                id = "cestmir-1741610376",
                communityId = community.id.toString(),
            ),
        )
    }

    @Test
    @Disabled("https://linear.app/herohero/issue/HH-3523/group-new-comment-notifications-on-single-post")
    fun `multiple comments on single post should on same day should create on grouped NEW_POST notification`() {
        val clock = TestClock(expectedTimestamp)
        val underTest = prepareFunction(clock)

        val post = testHelper.createPost("cestmir")
        val comment1 = testHelper.createPost("johny", parentId = post.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment1))

        val notifications1 = TestRepositories.notificationRepository.find { this }

        assertThat(notifications1).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "cestmir",
                type = NotificationType.NEW_COMMENT,
                actorIds = listOf("johny"),
                objectType = StorageEntityType.POST,
                objectId = comment1.id,
                created = expectedTimestamp,
                timestamp = expectedTimestamp,
                seenAt = null,
                checkedAt = null,
                id = "cestmir-1741610376",
            ),
        )

        clock += 10.seconds
        val comment2 = testHelper.createPost("filip", parentId = post.id)

        // should not create new notification, but group it with existent one
        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment2))

        val notifications2 = TestRepositories.notificationRepository.find { this }
        assertThat(notifications2).containsExactly(
            Notification(
                userId = "cestmir",
                type = NotificationType.NEW_COMMENT,
                actorIds = listOf("johny", "filip"),
                objectType = StorageEntityType.POST,
                objectId = comment1.id,
                created = expectedTimestamp,
                timestamp = expectedTimestamp,
                seenAt = null,
                checkedAt = null,
                id = "cestmir-1741610376",
            ),
        )
    }

    @Test
    fun `commenting a comment should create NEW_REPLY notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("johny", parentId = post.id)
        val reply = testHelper.createPost("cestmir", parentId = comment.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, reply))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).usingRecursiveFieldByFieldElementComparatorIgnoringFields("id").containsExactly(
            Notification(
                userId = "johny",
                type = NotificationType.NEW_REPLY,
                actorIds = listOf("cestmir"),
                objectType = StorageEntityType.POST,
                objectId = reply.id,
                created = expectedTimestamp,
                timestamp = expectedTimestamp,
                seenAt = null,
                checkedAt = null,
                id = "johny-1741610376",
            ),
        )
    }

    @Test
    fun `replying to a reply should create NEW_REPLY and NEW_REPLY_TO_REPLY notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("johny", parentId = post.id)
        val reply = testHelper.createPost("cestmir", parentId = comment.id)
        val replyToReply = testHelper.createPost("filip", parentId = comment.id, siblingId = reply.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, replyToReply))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("id")
            .containsExactlyInAnyOrder(
                Notification(
                    userId = "johny",
                    type = NotificationType.NEW_REPLY,
                    actorIds = listOf("filip"),
                    objectType = StorageEntityType.POST,
                    objectId = replyToReply.id,
                    created = expectedTimestamp,
                    timestamp = expectedTimestamp,
                    seenAt = null,
                    checkedAt = null,
                    id = "johny-1741610376",
                ),
                Notification(
                    userId = "cestmir",
                    type = NotificationType.NEW_REPLY_TO_REPLY,
                    actorIds = listOf("filip"),
                    objectType = StorageEntityType.POST,
                    objectId = replyToReply.id,
                    created = expectedTimestamp,
                    timestamp = expectedTimestamp,
                    seenAt = null,
                    checkedAt = null,
                    id = "cestmir-1741610376",
                ),
            )
    }

    @Test
    fun `replying to a reply should create one notification if the comment and the reply are from the same author`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("johny", parentId = post.id)
        val reply = testHelper.createPost("johny", parentId = comment.id)
        val replyToReply = testHelper.createPost("filip", parentId = comment.id, siblingId = reply.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, replyToReply))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("id")
            .containsExactlyInAnyOrder(
                Notification(
                    userId = "johny",
                    type = NotificationType.NEW_REPLY_TO_REPLY,
                    actorIds = listOf("filip"),
                    objectType = StorageEntityType.POST,
                    objectId = replyToReply.id,
                    created = expectedTimestamp,
                    timestamp = expectedTimestamp,
                    seenAt = null,
                    checkedAt = null,
                    id = "johny-1741610376",
                ),
            )
    }

    @Test
    fun `commenting my own post should not create any notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("cestmir", parentId = post.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).isEmpty()
    }

    @Test
    fun `replying to my comment should not create any notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost("cestmir")
        val comment = testHelper.createPost("cestmir", parentId = post.id)
        val reply = testHelper.createPost("cestmir", parentId = comment.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, reply))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).isEmpty()
    }

    @Test
    fun `commenting on livestream post that is live should not create any notification`() {
        val underTest = prepareFunction()

        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(
                PostAsset(
                    gjirafaLive = GjirafaLiveAsset(
                        id = "vjsnwfgo",
                        playbackUrl = "playback",
                        channelPublicId = "",
                        LiveVideoStatus.LIVE,
                    ),
                ),
            ),
        )
        val comment = testHelper.createPost("johny", parentId = post.id)

        underTest.consume(PostStateChanged(PostStateChange.PUBLISHED, comment))

        val notifications = TestRepositories.notificationRepository.find { this }
        assertThat(notifications).isEmpty()
    }

    private fun prepareFunction(testClock: TestClock = TestClock(expectedTimestamp)): CommentsNotifier =
        CommentsNotifier(
            firebaseMessaging = mockk(),
            postRepository = TestRepositories.postRepository,
            notificationRepository = TestRepositories.notificationRepository,
            userRepository = TestRepositories.userRepository,
            logger = TestLogger,
            systemEnv = TestEnvironmentVariables,
            clock = testClock,
        )
}
