package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.instantMin
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.gjirafa.GjirafaUploadsService
import hero.model.GjirafaAsset
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.GjirafaStatus.PROCESSING
import hero.model.Post
import hero.model.topics.Minutely
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import java.time.Instant
import java.time.LocalDateTime

@Suppress("Unused")
class PostStuckInProcessingAlerter(
    isProduction: Boolean = SystemEnv.isProduction,
) : PubSubSubscriber<Minutely>() {
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction)
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post)
    private val gjirafaService: GjirafaUploadsService = GjirafaUploadsService(
        projectId = SystemEnv.gjirafaProject,
        apiKey = SystemEnv.gjirafaApiKey,
        imageKey = SystemEnv.gjirafaImageKey,
    )
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject)

    override fun consume(payload: Minutely) {
        // consumer is run every ten minutes, one minute delayed
        if (LocalDateTime.now().minute % 10 != 1) {
            return
        }
        val postsInProcessing = postsCollection
            .where(Post::state).isEqualTo(PostState.PROCESSING)
            .fetchAll()

        val postsWithAssetsInProcessing = postsCollection
            .where(Post::assetStates).contains(PROCESSING)
            .and(Post::state).isNotIn(listOf(PostState.REVISION, PostState.DELETED))
            .fetchAll()

        val refreshedAssets = (postsInProcessing + postsWithAssetsInProcessing)
            .distinctBy { it.id }
            .flatMap { post ->
                post.assets
                    .mapNotNull { it.gjirafa }
                    .filter(::isLate)
                    .map { asset -> post to asset }
                    .map { (post, asset) -> refreshAsset(post, asset) }
            }

        refreshedAssets
            .groupBy({ it.first }, { it.second })
            .forEach { (post, assets) ->
                postsCollection[post.id].field(Post::assetStates).update(assets.map { it.status })
            }

        refreshedAssets
            .filter { (_, asset) -> isLate(asset) }
            .map { (post, asset) -> "\t${asset.createdAt} ${asset.id} ${asset.status.name} ${post.id}" }
            .let {
                // the `stuckPosts` property will be used to raise google email alert, see alerting.tf in Terraform
                if (it.isNotEmpty()) {
                    val message = listOf("Following ${it.size} posts are stuck in media processing:") + it
                    log.fatal(message.joinToString(System.lineSeparator()), mapOf("stuckPosts" to (it.size)))
                } else {
                    log.info("No assets are stuck in processing.", mapOf("stuckPosts" to 0))
                }
            }
    }

    // we consider the asset to be late if:
    // - it is processing after a fixed delay and 1.5x multiple of its length
    // - it is already partially completed after a fixed delay and 4x multiple of its length
    private val fixedDelay: Int = 300

    private fun isLate(asset: GjirafaAsset): Boolean =
        (
            asset.status == PROCESSING &&
                (asset.createdAt ?: instantMin) < Instant.now()
                    .minusSeconds((fixedDelay + asset.duration * 1.5).toLong())
        ).or(
            asset.status == PARTIALLY_COMPLETED &&
                (asset.createdAt ?: instantMin) < Instant.now()
                    .minusSeconds((fixedDelay + asset.duration * 4).toLong()),
        )

    private fun refreshAsset(
        post: Post,
        asset: GjirafaAsset,
    ): Pair<Post, GjirafaAsset> {
        val originalState = post.state
        val refreshedAsset = try {
            gjirafaService.getAsset(userId = null, assetId = asset.id, withDebug = false)
        } catch (e: Exception) {
            log.fatal("Failed to get asset ${asset.id}: ${e.message}")
            return post to asset
        }
        post.refreshAssets(refreshedAsset)
        postsCollection[post.id].field(Post::assets).update(post.assets)
        postsCollection[post.id].field(Post::state).update(post.state)
        if (post.state != originalState && post.state in setOf(PostState.PUBLISHED, PostState.SCHEDULED)) {
            pubSub.publish(PostStateChanged(PostStateChange.PUBLISHED, post))
        }

        val updatedAsset = post.assets.mapNotNull { it.gjirafa }.first { it.id == asset.id }
        return post to updatedAsset
    }
}
