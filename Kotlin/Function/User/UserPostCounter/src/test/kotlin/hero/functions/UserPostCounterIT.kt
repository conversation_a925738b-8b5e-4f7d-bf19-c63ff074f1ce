package hero.functions

import hero.model.SupportCounts
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class UserPostCounterIT : IntegrationTest() {
    private val expectedTimestamp = Instant.ofEpochSecond(1755450161)

    @Test
    fun `should update user post count and lastPostAt when post is published`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir", counts = SupportCounts(posts = 0))

        val post = testHelper.createPost(userId = "cestmir", state = PostState.PUBLISHED)

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        // updated user
        with(TestCollections.usersCollection["cestmir"].get()) {
            assertThat(counts.posts).isEqualTo(1)
            assertThat(lastPostAt).isEqualTo(expectedTimestamp)
        }
    }

    @Test
    fun `should ignore comments`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir", counts = SupportCounts(posts = 1))
        val parentPost = testHelper.createPost(userId = "cestmir", state = PostState.PUBLISHED)

        val comment = testHelper.createPost(
            userId = "cestmir",
            parentId = parentPost.id,
            state = PostState.PUBLISHED,
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = comment))

        with(TestCollections.usersCollection["cestmir"].get()) {
            assertThat(counts.posts).isEqualTo(1)
            assertThat(lastPostAt).isNull()
        }
    }

    @Test
    fun `should update category post count when post is published`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        testHelper.createCategory("cestmir", id = "category-id")

        val post = testHelper.createPost(
            userId = "cestmir",
            state = PostState.PUBLISHED,
            categories = listOf("category-id"),
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        with(TestCollections.categoriesCollection["category-id"].get()) {
            assertThat(postCount).isEqualTo(1)
        }
    }

    @Test
    fun `should handle message thread messages count correctly`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        testHelper.createUser("participant")

        val messageThread = testHelper.createMessageThread(
            userId = "cestmir",
            participants = listOf("participant"),
            id = "message-thread-id",
        )
        TestCollections.messageThreadsCollection["message-thread-id"].set(messageThread)

        val post = testHelper.createPost(
            userId = "cestmir",
            messageThreadId = "message-thread-id",
            state = PostState.PUBLISHED,
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        val updatedMessageThread = TestCollections.messageThreadsCollection["message-thread-id"].get()
        assertThat(updatedMessageThread.posts).isEqualTo(1)
        assertThat(updatedMessageThread.lastMessageAt).isEqualTo(post.created)
        assertThat(updatedMessageThread.lastMessageBy).isEqualTo("cestmir")
        assertThat(updatedMessageThread.lastMessageId).isEqualTo(post.id)
        assertThat(updatedMessageThread.emailNotified).isFalse()
    }

    @Test
    fun `should count only published posts for user`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir", counts = SupportCounts(posts = 0))

        // Create posts with different states
        testHelper.createPost(userId = "cestmir", state = PostState.PUBLISHED)
        testHelper.createPost(userId = "cestmir", state = PostState.SCHEDULED)
        testHelper.createPost(userId = "cestmir", state = PostState.DELETED)
        testHelper.createPost(userId = "cestmir", state = PostState.PROCESSING)

        val newPost = testHelper.createPost(
            userId = "cestmir",
            state = PostState.PUBLISHED,
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = newPost))

        with(TestCollections.usersCollection["cestmir"].get()) {
            assertThat(counts.posts).isEqualTo(2)
        }
    }

    @Test
    fun `should handle multiple categories for a single post`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir")
        testHelper.createCategory("cestmir", id = "category-1")
        testHelper.createCategory("cestmir", id = "category-2")

        val post = testHelper.createPost(
            userId = "cestmir",
            state = PostState.PUBLISHED,
            categories = listOf("category-1", "category-2"),
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        listOf("category-1", "category-2").forEach { categoryId ->
            val updatedCategory = TestCollections.categoriesCollection[categoryId].get()
            assertThat(updatedCategory.postCount).isEqualTo(1)
        }
    }

    @Test
    fun `should exclude community posts from user post count`() {
        val underTest = prepareFunction()

        testHelper.createUser("cestmir", counts = SupportCounts(posts = 0))
        val community = testHelper.createCommunity("cestmir")

        val post = testHelper.createPost(
            userId = "cestmir",
            state = PostState.PUBLISHED,
            communityId = community.id,
        )

        underTest.consume(PostStateChanged(stateChange = PostStateChange.PUBLISHED, post = post))

        // Community posts should not be counted in user post count
        with(TestCollections.usersCollection["cestmir"].get()) {
            assertThat(counts.posts).isEqualTo(0)
        }
    }

    private fun prepareFunction(): UserPostCounter =
        UserPostCounter(
            lazyContext = lazyTestContext,
            firestore = firestore,
            usersCollection = TestCollections.usersCollection,
            categoriesCollection = TestCollections.categoriesCollection,
            messageThreadsCollection = TestCollections.messageThreadsCollection,
            clock = TestClock(expectedTimestamp),
            logger = TestLogger,
            envVariables = TestEnvironmentVariables,
        )
}
