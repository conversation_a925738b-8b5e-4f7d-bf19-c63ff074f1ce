package hero.functions

import hero.baseutils.escapeUrl
import hero.baseutils.log
import hero.baseutils.nullIfEmpty
import hero.baseutils.truncate
import hero.core.logging.Logger
import hero.gcloud.imageProxy
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.PostAsset
import hero.model.SpotifyCredentials
import hero.model.User
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.RSS_FEED
import org.apache.commons.text.StringEscapeUtils.escapeXml10
import org.jooq.DSLContext
import org.jooq.XML
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID

class RssFeedGenerator(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    private val postRepository: PostRepository = PostRepository(lazyContext),
    private val logger: Logger = log,
) {
    private val context by lazyContext

    fun execute(command: GenerateAndStoreRssFeed): GeneratedRssFeeds {
        logger.info("Generating rss feed for ${command.creator.id}")
        val creator = command.creator
        val posts = postRepository.find {
            this
                .where(POST.USER_ID.eq(creator.id))
                .and(POST.TYPE.eq(PostType.CONTENT_POST.name))
                .and(POST.STATE.eq(PostState.PUBLISHED.name))
                .orderBy(POST.PUBLISHED_AT.desc())
        }
        val placeholderToken = UUID.randomUUID().toString().replace("-", "")

        val minimalDtoPosts = posts.map {
            PostMinimalDto(
                id = it.id,
                text = it.text,
                title = it.title,
                publishedAt = it.published,
                excludeFromRss = it.excludeFromRss,
                assets = it.assets,
            )
        }

        val basicRssFeed = generateRssXmlFeed(
            posts = minimalDtoPosts.asSequence(),
            creator = creator,
            token = placeholderToken,
            spotifyCredentials = null,
            envPrefix = command.envPrefix,
        )

        val spotifyRssFeed = generateRssXmlFeed(
            posts = minimalDtoPosts.asSequence(),
            creator = creator,
            token = placeholderToken,
            spotifyCredentials = command.credentials,
            envPrefix = command.envPrefix,
        )

        saveRssFeed(basicRssFeed, creator.id, RssFeedType.BASIC, placeholderToken)
        saveRssFeed(spotifyRssFeed, creator.id, RssFeedType.SPOTIFY, placeholderToken)

        logger.info("Done generating rss feed for ${command.creator.id}")

        return GeneratedRssFeeds(
            basic = basicRssFeed,
            spotify = spotifyRssFeed,
            placeholderToken = placeholderToken,
        )
    }

    fun execute(query: FindRssFeed): GeneratedRssFeeds? {
        val feeds = context
            .select(RSS_FEED.XML, RSS_FEED.TYPE, RSS_FEED.PLACEHOLDER_TOKEN)
            .from(RSS_FEED)
            .where(RSS_FEED.USER_ID.eq(query.creatorId))
            .fetch()

        val feedsByType = feeds.associate { RssFeedType.valueOf(it[RSS_FEED.TYPE]) to it[RSS_FEED.XML].data() }

        if (feedsByType.size != RssFeedType.entries.size) {
            return null
        }

        return GeneratedRssFeeds(
            basic = feedsByType.getValue(RssFeedType.BASIC),
            spotify = feedsByType.getValue(RssFeedType.SPOTIFY),
            placeholderToken = feeds.first()[RSS_FEED.PLACEHOLDER_TOKEN],
        )
    }

    private fun saveRssFeed(
        feed: String,
        creatorId: String,
        type: RssFeedType,
        placeholderToken: String,
    ) {
        val now = Instant.now()

        context
            .insertInto(RSS_FEED)
            .set(RSS_FEED.USER_ID, creatorId)
            .set(RSS_FEED.TYPE, type.name)
            .set(RSS_FEED.XML, XML.valueOf(feed))
            .set(RSS_FEED.PLACEHOLDER_TOKEN, placeholderToken)
            .set(RSS_FEED.CREATED_AT, now)
            .set(RSS_FEED.UPDATED_AT, now)
            .onConflict(RSS_FEED.USER_ID, RSS_FEED.TYPE)
            .doUpdate()
            .set(RSS_FEED.XML, XML.valueOf(feed))
            .set(RSS_FEED.PLACEHOLDER_TOKEN, placeholderToken)
            .set(RSS_FEED.UPDATED_AT, now)
            .execute()
    }
}

data class GenerateAndStoreRssFeed(val creator: User, val envPrefix: String, val credentials: SpotifyCredentials)

data class GeneratedRssFeeds(val basic: String, val spotify: String, val placeholderToken: String)

data class FindRssFeed(val creatorId: String)

private fun generateRssXmlFeed(
    posts: Sequence<PostMinimalDto>,
    creator: User,
    token: String,
    spotifyCredentials: SpotifyCredentials?,
    envPrefix: String,
): String {
    // for image cropping below see:
    // - https://docs.bunny.net/docs/stream-image-processing
    // email is hidden as proposed here:
    // - https://herohero-workspace.slack.com/archives/C01B8L52FL3/p1665773469077539?thread_ts=1665763897.250619&cid=C01B8L52FL3
    val creatorName = escapeXml10(creator.name)

    val body = posts
        // https://linear.app/herohero/issue/HH-563/add-option-to-exclude-post-from-rss-feed
        .filter { !it.excludeFromRss }
        // we should hide planned posts even for authors/creators
        .filter { postDto -> postDto.publishedAt < Instant.now() }
        // https://linear.app/herohero/issue/HH-410/rss-feed-update
        // Before Gjirafa media implementation, creators had to upload both video and audio versions of
        // posts. Now, they need to upload only video version and audio is automatically extracted. So for
        // these legacy Mux videos, in case a post contains exactly two assets from which one is audio,
        // the other video, we intentionally drop the video asset to avoid duplicates.
        .flatMap { postDto ->
            // the post mux asset must have an audio track, otherwise we can skip it
            val assets = postDto.assets.filter { it.gjirafa?.hasAudio == true }
            val audioAsset = assets.firstOrNull { it.gjirafa?.hasVideo == false }
            val videoAsset = assets.firstOrNull { it.gjirafa?.hasVideo == true }
            val image = postDto.assets.firstNotNullOfOrNull { it.image }
            if (assets.size == 2 && audioAsset != null && videoAsset != null && audioAsset != videoAsset) {
                // selecting only audio asset as described above
                listOf(Triple(postDto, audioAsset.gjirafa!!, audioAsset.thumbnail ?: image?.id))
            } else {
                // selecting all assets
                assets
                    .mapNotNull {
                        (
                            it.gjirafa
                                ?.takeIf { gjirafa -> gjirafa.status in setOf(COMPLETE, PARTIALLY_COMPLETED) }
                                ?.let { gjirafa -> Triple(postDto, gjirafa, it.thumbnail ?: image?.id) }
                        )
                    }
            }
        }
        .filter { (_, gjirafa, _) -> gjirafa.audioStaticUrl != null }
        // there must be a static rendition
        .map { (postDto, gjirafa, thumbnail) ->
            val enclosureUrl = gjirafa.audioStaticUrl
            val title = (
                postDto.title?.nullIfEmpty()?.let { escapeXml10(it) }
                    ?: escapeXml10(postDto.text.replace("\\s+".toRegex(), " ").truncate(100))
            )?.takeIf { it.isNotEmpty() } ?: "Untitled"
            // description must be empty for Spotify, see HH-2680
            val description = if (spotifyCredentials != null)
                ""
            else
                escapeXml10(postDto.text)
                    .takeIf { it.isNotEmpty() }
                    ?: escapeXml10(creator.name)
            val publishDate = postDto.publishedAt.atZone(ZoneOffset.UTC)
                .format(DateTimeFormatter.RFC_1123_DATE_TIME)
            val guid = escapeXml10(postDto.id)
            val link = "https://herohero.co/${creator.path}/post/${postDto.id.escapeUrl()}"
            val length = gjirafa.audioByteSize ?: 0
            val duration = gjirafa.duration.toInt()

            """
                <item>
                    <title>$title</title>
                    <description>$description</description>
                    <pubDate>$publishDate</pubDate>
                    <guid isPermaLink="false">$guid</guid>
                    <link>$link</link>
                    ${renderImageTag(thumbnail, 3000)}
                    ${if (spotifyCredentials != null) renderSpotify(entitlement = creator.id) else ""}
                    <enclosure url="$enclosureUrl" type="audio/x-m4a" length="$length"/>
                    <itunes:duration>$duration</itunes:duration>
                </item>
                """
        }

    // until now, the process can fail anytime, so we must not write anything to body
    // to be able to cache the error responses
    return buildString {
        append(
            """
            <?xml version="1.0" encoding="UTF-8"?>
            <rss version="2.0"
                xmlns:atom="http://www.w3.org/2005/Atom"
                xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd"
                xmlns:spotify="http://www.spotify.com/ns/rss"
            >
            <channel>
                <title>$creatorName</title>
                <description>${escapeXml10(creator.bio)}</description>
                <language>cs-cz</language>
                <link>https://herohero.co/${creator.path}</link>
                <atom:link rel="self" type="application/rss+xml" href="https://$envPrefix.herohero.co/rss-feed/?token=${token.escapeUrl()}"/>
                <itunes:author>${if (spotifyCredentials != null) "Herohero" else creatorName}</itunes:author>
                ${renderImageTag(creator.image?.id?.imageProxy(), 1400)}
                <itunes:category text="Society &amp; Culture"/>
                <itunes:block>yes</itunes:block>
                <itunes:owner>
                    <itunes:name>$creatorName</itunes:name>
                    <itunes:email>${if (spotifyCredentials != null) creator.email else "<EMAIL>"}</itunes:email>
                </itunes:owner>
                ${if (spotifyCredentials != null) renderSpotify(partnerId = spotifyCredentials.partnerId) else ""}
            """.trimXmlIndent(),
        )

        body.forEach { append(it.trimXmlIndent()) }

        append(
            """
              </channel>
            </rss>
            """.trimXmlIndent(),
        )
    }
}

private fun renderImageTag(
    url: String?,
    size: Int,
): String {
    if (url.isNullOrBlank()) {
        return ""
    }
    // https://docs.bunny.net/docs/stream-image-processing
    val formattedUrl = "${escapeXml10(url)}?width=$size&amp;height=$size&amp;aspect_ratio=1:1"
    return "<itunes:image href=\"$formattedUrl\"/>"
}

@OptIn(ExperimentalStdlibApi::class)
private fun renderSpotify(
    partnerId: String? = null,
    entitlement: String? = null,
): String =
    """
        <spotify:access>
            ${if (partnerId != null) "<partner id=\"$partnerId\"/>" else ""}
            ${if (entitlement != null) "<entitlement name=\"${entitlement.toByteArray().toHexString()}\"/>" else ""}
        </spotify:access>
        """.trimXmlIndent()

data class PostMinimalDto(
    val id: String,
    val text: String,
    val title: String?,
    val publishedAt: Instant,
    val excludeFromRss: Boolean,
    val assets: List<PostAsset>,
)

fun String.trimXmlIndent() =
    trim()
        // note that .trimIndent() doesn't work with multiline bios
        .replace("^\\s+".toRegex(RegexOption.MULTILINE), "")

enum class RssFeedType {
    BASIC,
    SPOTIFY,
}
