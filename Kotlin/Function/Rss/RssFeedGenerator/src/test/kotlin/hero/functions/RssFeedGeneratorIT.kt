package hero.functions

import hero.baseutils.plusDays
import hero.baseutils.truncated
import hero.model.GjirafaAsset
import hero.model.GjirafaStatus
import hero.model.PostAsset
import hero.model.SpotifyCredentials
import hero.model.topics.PostState
import hero.sql.jooq.Tables
import hero.sql.jooq.tables.records.RssFeedRecord
import hero.test.IntegrationTest
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.jooq.XML
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.w3c.dom.Document
import java.io.ByteArrayInputStream
import java.io.StringWriter
import java.time.Instant
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.transform.OutputKeys
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult

class RssFeedGeneratorIT : IntegrationTest() {
    @Nested
    inner class GenerateAndStoreRssFeed {
        @Test
        fun `should generate basic and spotify rss feed and store it`() {
            val creator = testHelper.createUser(
                "cestmir",
                name = "cestmir strakaty",
                hasRssFeed = true,
                email = "<EMAIL>",
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "no-audio-post-id",
                text = "no audio",
                assets = listOf(gjirafaAsset("b-no-audio", hasAudio = false)),
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "excluded-rss-post-id",
                text = "excluded from rss",
                assets = listOf(gjirafaAsset("b-excluded-post", hasAudio = true)),
                excludedFromRss = true,
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "processing-post-id",
                text = "post currently processing",
                publishedAt = Instant.now(),
                state = PostState.PROCESSING,
                assets = listOf(gjirafaAsset("b-scheduled-post", hasAudio = true, status = GjirafaStatus.PROCESSING)),
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "scheduled-post-id",
                text = "is scheduled in the future",
                publishedAt = Instant.now().plusDays(1),
                assets = listOf(gjirafaAsset("b-scheduled-post", hasAudio = true)),
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "rss-post1",
                title = "",
                text = "rss-post1 should be in feed",
                publishedAt = Instant.ofEpochSecond(1741869368),
                assets = listOf(gjirafaAsset("brss-post1", hasAudio = true)),
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "rss-post2",
                title = "rss post 2 title",
                text = "rss-post2 should be in feed, but has both audio and video",
                publishedAt = Instant.ofEpochSecond(1741782968),
                assets = listOf(
                    gjirafaAsset("brss-post2", hasAudio = true),
                    gjirafaAsset("vrss-post2", hasAudio = true, hasVideo = true),
                ),
            )
            testHelper.createPost(
                userId = "cestmir",
                id = "rss-post3",
                title = null,
                text = "rss-post3 should be in feed, has two audios, both should be in the feed",
                publishedAt = Instant.ofEpochSecond(1741696568),
                assets = listOf(
                    gjirafaAsset("brss-post3-1", hasAudio = true),
                    gjirafaAsset("brss-post3-2", hasAudio = true),
                ),
            )

            val underTest = RssFeedGenerator(
                lazyTestContext,
                TestRepositories.postRepository,
                TestLogger,
            )

            val generatedRssFeed = underTest.execute(
                GenerateAndStoreRssFeed(
                    creator = creator,
                    "test",
                    SpotifyCredentials("", "", "herohero", ""),
                ),
            )

            val basicRssFeed = testContext.selectFrom(Tables.RSS_FEED)
                .where(Tables.RSS_FEED.USER_ID.eq("cestmir"))
                .and(Tables.RSS_FEED.TYPE.eq(RssFeedType.BASIC.name))
                .fetchSingle()

            assertThat(generatedRssFeed.placeholderToken).isEqualTo(basicRssFeed.placeholderToken)
            val basicRssFeedXml = parseXml(basicRssFeed.xml.data())
            val expectedBasicRssFeedXml = parseXml(
                """
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" xmlns:spotify="http://www.spotify.com/ns/rss" version="2.0">
    <channel>
        <title>cestmir strakaty</title>
        <description>bio</description>
        <language>cs-cz</language>
        <link>https://herohero.co/cestmir</link>
        <atom:link href="https://test.herohero.co/rss-feed/?token=${basicRssFeed.placeholderToken}" rel="self" type="application/rss+xml"/>
        <itunes:author>cestmir strakaty</itunes:author>
        <itunes:category text="Society &amp; Culture"/>
        <itunes:block>yes</itunes:block>
        <itunes:owner>
            <itunes:name>cestmir strakaty</itunes:name>
            <itunes:email><EMAIL></itunes:email>
        </itunes:owner>
        <item>
            <title>rss-post1 should be in feed</title>
            <description>rss-post1 should be in feed</description>
            <pubDate>Thu, 13 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post1</guid>
            <link>https://herohero.co/cestmir/post/rss-post1</link>
            <itunes:image href="https://thumbnails.com/brss-post1?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <enclosure length="0" type="audio/x-m4a" url="brss-post1-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss post 2 title</title>
            <description>rss-post2 should be in feed, but has both audio and video</description>
            <pubDate>Wed, 12 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post2</guid>
            <link>https://herohero.co/cestmir/post/rss-post2</link>
            <itunes:image href="https://thumbnails.com/brss-post2?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <enclosure length="0" type="audio/x-m4a" url="brss-post2-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss-post3 should be in feed, has two audios, both should be in the feed</title>
            <description>rss-post3 should be in feed, has two audios, both should be in the feed</description>
            <pubDate>Tue, 11 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post3</guid>
            <link>https://herohero.co/cestmir/post/rss-post3</link>
            <itunes:image href="https://thumbnails.com/brss-post3-1?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <enclosure length="0" type="audio/x-m4a" url="brss-post3-1-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss-post3 should be in feed, has two audios, both should be in the feed</title>
            <description>rss-post3 should be in feed, has two audios, both should be in the feed</description>
            <pubDate>Tue, 11 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post3</guid>
            <link>https://herohero.co/cestmir/post/rss-post3</link>
            <itunes:image href="https://thumbnails.com/brss-post3-2?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <enclosure length="0" type="audio/x-m4a" url="brss-post3-2-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
    </channel>
</rss>
            """.trimXmlIndent(),
            )
            assertThat(basicRssFeedXml.toPrettyString()).isEqualTo(expectedBasicRssFeedXml.toPrettyString())
            assertThat(
                parseXml(generatedRssFeed.basic).toPrettyString(),
            ).isEqualTo(expectedBasicRssFeedXml.toPrettyString())

            val spotifyRssFeed = testContext.selectFrom(Tables.RSS_FEED)
                .where(Tables.RSS_FEED.USER_ID.eq("cestmir"))
                .and(Tables.RSS_FEED.TYPE.eq(RssFeedType.SPOTIFY.name))
                .fetchSingle()

            val spotifyRssFeedXml = parseXml(spotifyRssFeed.xml.data())
            val expectedSpotifyRssFeedXml = parseXml(
                """
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<rss xmlns:atom="http://www.w3.org/2005/Atom" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" xmlns:spotify="http://www.spotify.com/ns/rss" version="2.0">
    <channel>
        <title>cestmir strakaty</title>
        <description>bio</description>
        <language>cs-cz</language>
        <link>https://herohero.co/cestmir</link>
        <atom:link href="https://test.herohero.co/rss-feed/?token=${spotifyRssFeed.placeholderToken}" rel="self" type="application/rss+xml"/>
        <itunes:author>Herohero</itunes:author>
        <itunes:category text="Society &amp; Culture"/>
        <itunes:block>yes</itunes:block>
        <itunes:owner>
            <itunes:name>cestmir strakaty</itunes:name>
            <itunes:email><EMAIL></itunes:email>
        </itunes:owner>
        <spotify:access>
            <partner id="herohero"/>
        </spotify:access>
        <item>
            <title>rss-post1 should be in feed</title>
            <description></description>
            <pubDate>Thu, 13 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post1</guid>
            <link>https://herohero.co/cestmir/post/rss-post1</link>
            <itunes:image href="https://thumbnails.com/brss-post1?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <spotify:access>
                <entitlement name="636573746d6972"/>
            </spotify:access>
            <enclosure length="0" type="audio/x-m4a" url="brss-post1-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss post 2 title</title>
            <description></description>
            <pubDate>Wed, 12 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post2</guid>
            <link>https://herohero.co/cestmir/post/rss-post2</link>
            <itunes:image href="https://thumbnails.com/brss-post2?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <spotify:access>
                <entitlement name="636573746d6972"/>
            </spotify:access>
            <enclosure length="0" type="audio/x-m4a" url="brss-post2-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss-post3 should be in feed, has two audios, both should be in the feed</title>
            <description></description>
            <pubDate>Tue, 11 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post3</guid>
            <link>https://herohero.co/cestmir/post/rss-post3</link>
            <itunes:image href="https://thumbnails.com/brss-post3-1?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <spotify:access>
                <entitlement name="636573746d6972"/>
            </spotify:access>
            <enclosure length="0" type="audio/x-m4a" url="brss-post3-1-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
        <item>
            <title>rss-post3 should be in feed, has two audios, both should be in the feed</title>
            <description></description>
            <pubDate>Tue, 11 Mar 2025 12:36:08 GMT</pubDate>
            <guid isPermaLink="false">rss-post3</guid>
            <link>https://herohero.co/cestmir/post/rss-post3</link>
            <itunes:image href="https://thumbnails.com/brss-post3-2?width=3000&amp;height=3000&amp;aspect_ratio=1:1"/>
            <spotify:access>
                <entitlement name="636573746d6972"/>
            </spotify:access>
            <enclosure length="0" type="audio/x-m4a" url="brss-post3-2-audio-url"/>
            <itunes:duration>0</itunes:duration>
        </item>
    </channel>
</rss>
            """.trimXmlIndent(),
            )
            assertThat(spotifyRssFeedXml.toPrettyString()).isEqualTo(expectedSpotifyRssFeedXml.toPrettyString())
            assertThat(
                parseXml(generatedRssFeed.spotify).toPrettyString(),
            ).isEqualTo(expectedSpotifyRssFeedXml.toPrettyString())
        }

        @Test
        fun `regenerating rss feed should update placeholder token`() {
            val creator = testHelper.createUser("cestmir", name = "cestmir strakaty")
            val underTest = RssFeedGenerator(
                lazyTestContext,
                TestRepositories.postRepository,
                TestLogger,
            )

            val now = Instant.now().truncated()
            val spotifyRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.SPOTIFY.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>spotify</rss>")
                createdAt = now
                updatedAt = now
            }

            testContext
                .insertInto(Tables.RSS_FEED)
                .set(spotifyRssFeed)
                .execute()

            underTest.execute(
                GenerateAndStoreRssFeed(
                    creator = creator,
                    "test",
                    SpotifyCredentials("", "", "herohero", ""),
                ),
            )

            val rssFeed = testContext
                .selectFrom(Tables.RSS_FEED)
                .where(Tables.RSS_FEED.TYPE.equal(RssFeedType.SPOTIFY.name))
                .fetchSingle()

            assertThat(rssFeed[Tables.RSS_FEED.XML].data()).isNotEqualTo(spotifyRssFeed.xml.data())
            assertThat(rssFeed[Tables.RSS_FEED.PLACEHOLDER_TOKEN]).isNotEqualTo(spotifyRssFeed.placeholderToken)
            assertThat(rssFeed[Tables.RSS_FEED.CREATED_AT]).isEqualTo(spotifyRssFeed.createdAt)
            // todo might be flaky, introduce clock in that case
            assertThat(rssFeed[Tables.RSS_FEED.UPDATED_AT]).isNotEqualTo(spotifyRssFeed.createdAt)
        }
    }

    @Nested
    inner class FindRssFeed {
        @Test
        fun `should find existing rss feeds for both spotify and basic`() {
            val underTest = RssFeedGenerator(
                lazyTestContext,
                TestRepositories.postRepository,
                TestLogger,
            )

            testHelper.createUser("cestmir")
            val basicRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.BASIC.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>basic</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            val spotifyRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.SPOTIFY.name
                placeholderToken = "placeholder"
                xml = XML.valueOf("<rss>spotify</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            testContext
                .insertInto(Tables.RSS_FEED)
                .set(basicRssFeed, spotifyRssFeed)
                .execute()

            val result = underTest.execute(FindRssFeed("cestmir"))

            assertThat(result).isEqualTo(GeneratedRssFeeds("<rss>basic</rss>", "<rss>spotify</rss>", "placeholder"))
        }

        @Test
        fun `should return nothing if one of the types is missing`() {
            val underTest = RssFeedGenerator(
                lazyTestContext,
                TestRepositories.postRepository,
                TestLogger,
            )

            testHelper.createUser("cestmir")
            val basicRssFeed = RssFeedRecord().apply {
                userId = "cestmir"
                type = RssFeedType.BASIC.name
                placeholderToken = "basic-placeholder"
                xml = XML.valueOf("<rss>basic</rss>")
                createdAt = Instant.now()
                updatedAt = Instant.now()
            }

            testContext
                .insertInto(Tables.RSS_FEED)
                .set(basicRssFeed)
                .execute()

            val result = underTest.execute(FindRssFeed("cestmir"))

            assertThat(result).isNull()
        }
    }
}

fun gjirafaAsset(
    id: String,
    hasAudio: Boolean = true,
    hasVideo: Boolean = false,
    status: GjirafaStatus = GjirafaStatus.COMPLETE,
) = PostAsset(
    gjirafa = GjirafaAsset(
        key = "encode-key",
        audioByteSize = 0,
        audioStaticUrl = "$id-audio-url",
        audioStreamUrl = "audio-stream",
        thumbnailBlurhash = "LGSF;JIUofof00RjWBay4nofofj[",
        chaptersVttUrl = "chapters-vtt-url",
        duration = 0.0,
        hasAudio = hasAudio,
        hasVideo = hasVideo,
        hidden = false,
        id = id,
        status = status,
        encodingFinishTime = null,
        encodingRemainingTime = null,
        createdAt = null,
        projectId = "project-id",
        imageKey = "1264567897",
    ),
    thumbnail = "https://thumbnails.com/$id",
)

fun parseXml(xml: String): Document {
    val factory = DocumentBuilderFactory.newInstance()
    factory.isIgnoringElementContentWhitespace = true
    factory.isNamespaceAware = true
    val builder = factory.newDocumentBuilder()
    return builder.parse(
        ByteArrayInputStream(
            xml
                .lines()
                .filter { it.isNotBlank() }
                .joinToString("\n")
                .toByteArray(),
        ),
    )
}

fun Document.toPrettyString(): String {
    val transformer = TransformerFactory.newInstance().newTransformer()
    transformer.setOutputProperty(OutputKeys.INDENT, "yes")
    val writer = StringWriter()
    transformer.transform(DOMSource(this), StreamResult(writer))

    return writer
        .toString()
        .lines()
        .filter { it.isNotBlank() }
        .joinToString("\n")
}
