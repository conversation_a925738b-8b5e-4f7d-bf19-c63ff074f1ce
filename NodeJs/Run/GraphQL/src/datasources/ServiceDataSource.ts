import { AugmentedRequest, DataSourceConfig, RESTDataSource } from '@apollo/datasource-rest'
import { Environment } from '../common/environment'
import { userAgent } from './common-utils'

export abstract class ServiceDataSource extends RESTDataSource {
    environment: Environment
    override baseURL: string
    cookies?: string
    serviceName: string

    protected constructor(
        environment: Environment,
        serviceName: 'api' | 'media' | 'auth',
        cookies?: string,
        config?: DataSourceConfig
    ) {
        super(config)
        this.environment = environment
        this.serviceName = serviceName
        const envPrefix = environment === Environment.LOCAL ? Environment.DEVEL : environment
        this.baseURL = `https://${envPrefix}-${serviceName}-90568653510.europe-west1.run.app`
        // this.baseURL = `http://localhost:8080`
        this.cookies = cookies
    }

    override willSendRequest(_: string, request: AugmentedRequest) {
        request.headers['User-Agent'] = userAgent()
        if (this.cookies) {
            request.headers.Cookie = this.cookies
        }
    }
}
